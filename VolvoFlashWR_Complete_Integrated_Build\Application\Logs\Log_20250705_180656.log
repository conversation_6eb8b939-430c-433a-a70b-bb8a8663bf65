Log started at 7/5/2025 6:06:56 PM
2025-07-05 18:06:56.294 [Information] LoggingService: Logging service initialized
2025-07-05 18:06:56.307 [Information] App: Starting integrated application initialization
2025-07-05 18:06:56.309 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 18:06:56.311 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 18:06:56.319 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 18:06:56.320 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 18:06:56.322 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 18:06:56.325 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 18:06:56.336 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 18:06:56.349 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 18:06:56.356 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:06:56.364 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:06:56.372 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:06:56.379 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 18:06:56.386 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 18:06:56.387 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 18:06:56.390 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 18:06:56.394 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 18:06:56.394 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 18:06:56.396 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 18:06:56.397 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:06:56.398 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:06:56.398 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:06:56.409 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 18:06:56.409 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 18:06:56.410 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 18:06:56.423 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 18:06:56.423 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 18:06:56.426 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 18:06:56.428 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 18:06:56.441 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 18:06:56.447 [Information] LibraryExtractor: Copying system libraries
2025-07-05 18:06:56.462 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 18:06:56.469 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 18:07:19.802 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 18:08:02.768 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 18:14:18.722 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-05 18:15:05.838 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:15:50.899 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:16:39.407 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:17:24.516 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 18:17:24.517 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 18:17:24.517 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 18:17:24.518 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 18:17:24.518 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 18:17:24.518 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 18:17:24.529 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 18:17:24.531 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 18:17:24.532 [Information] DependencyManager: Initializing dependency manager
2025-07-05 18:17:24.533 [Information] DependencyManager: Setting up library search paths
2025-07-05 18:17:24.535 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:17:24.535 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 18:17:24.537 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 18:17:24.537 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 18:17:24.543 [Information] DependencyManager: Verifying required directories
2025-07-05 18:17:24.543 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:17:24.544 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 18:17:24.544 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 18:17:24.544 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 18:17:24.548 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 18:17:24.566 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 18:17:24.568 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 18:17:24.577 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 18:17:24.580 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 18:17:24.581 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 18:17:24.582 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:17:24.583 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:17:24.584 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:17:24.586 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 18:17:24.587 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-05 18:17:24.588 [Warning] DependencyManager: Failed to load Critical library WUDFPuma.dll: Error 193
2025-07-05 18:17:24.591 [Information] DependencyManager: ✓ Loaded Critical library: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll (x86)
2025-07-05 18:17:24.596 [Information] DependencyManager: ✓ Loaded Critical library: apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll (x86)
2025-07-05 18:17:24.597 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlus.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll (x86)
2025-07-05 18:17:24.599 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll (x86)
2025-07-05 18:17:24.600 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll (x86)
2025-07-05 18:17:24.601 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 18:17:24.602 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 18:17:24.603 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 18:17:24.604 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 18:17:24.605 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 18:17:24.606 [Information] DependencyManager: Setting up environment variables
2025-07-05 18:17:24.607 [Information] DependencyManager: Environment variables configured
2025-07-05 18:17:24.614 [Information] DependencyManager: Verifying library loading status
2025-07-05 18:17:25.079 [Information] DependencyManager: Library loading verification: 9/11 (81.8%) critical libraries loaded
2025-07-05 18:17:25.080 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 18:17:25.084 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 18:17:25.086 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 18:17:25.090 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 18:17:25.096 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 18:17:25.097 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 18:17:25.097 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 18:17:25.101 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 18:17:25.102 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 18:17:25.102 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:17:25.102 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 18:17:25.103 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 18:17:25.103 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 18:17:25.103 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 18:17:25.104 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:17:25.104 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 18:17:25.104 [Information] App: Integrated startup completed successfully
2025-07-05 18:17:25.108 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 18:17:25.141 [Information] App: Initializing application services
2025-07-05 18:17:25.143 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 18:17:25.144 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 18:17:25.204 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 18:17:25.205 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 18:17:25.206 [Information] App: Configuration service initialized successfully
2025-07-05 18:17:25.207 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 18:17:25.208 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 18:17:25.213 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 18:17:25.213 [Information] App: Final useDummyImplementations value: False
2025-07-05 18:17:25.214 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 18:17:25.228 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 18:17:25.229 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 18:17:25.230 [Information] App: usePatchedImplementation flag is: True
2025-07-05 18:17:25.230 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 18:17:25.230 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 18:17:25.231 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 18:17:25.231 [Information] App: verboseLogging flag is: True
2025-07-05 18:17:25.236 [Information] App: Verifying real hardware requirements...
2025-07-05 18:17:25.237 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 18:17:25.237 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 18:17:25.237 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 18:17:25.238 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 18:17:25.238 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 18:17:25.239 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 18:17:25.239 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 18:17:25.239 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 18:17:25.251 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 18:17:25.255 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 18:17:25.257 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 18:17:25.264 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 18:17:25.265 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 18:17:25.265 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 18:17:25.266 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 18:17:25.266 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 18:17:25.266 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 18:17:25.266 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 18:17:25.267 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 18:17:25.267 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 18:17:25.268 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 18:17:25.269 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x86
2025-07-05 18:17:25.274 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: apci.dll
2025-07-05 18:17:25.275 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: Volvo.ApciPlus.dll
2025-07-05 18:17:25.276 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: WUDFPuma.dll
2025-07-05 18:17:25.276 [Warning] ArchitectureAwareVocomServiceFactory: Found 1 incompatible libraries
2025-07-05 18:17:25.278 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 18:17:25.278 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 18:17:25.279 [Warning] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - bridge not yet implemented, falling back to dummy mode
2025-07-05 18:17:25.281 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 18:17:25.282 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 18:17:25.283 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 18:17:25.283 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-05 18:17:25.283 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-05 18:17:25.284 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 18:17:25.344 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 18:17:25.445 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 18:17:25.446 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 18:17:25.448 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 18:17:25.649 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 18:17:25.649 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-05 18:17:25.663 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 18:17:25.667 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:25.667 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 18:17:25.671 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 18:17:25.674 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 18:17:25.675 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 18:17:25.683 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 18:17:25.692 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 18:17:25.697 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 18:17:25.708 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 18:17:25.710 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 18:17:25.731 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:17:25.734 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:17:25.746 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:17:25.748 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 18:17:25.748 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 18:17:25.749 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:17:25.749 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:17:25.749 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 18:17:25.750 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:17:25.750 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 18:17:25.750 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 18:17:25.752 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 18:17:25.752 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 18:17:25.753 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 18:17:25.753 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 18:17:25.753 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 18:17:25.754 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 18:17:25.754 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 18:17:25.754 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 18:17:25.760 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 18:17:25.766 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 18:17:25.767 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 18:17:25.783 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 18:17:25.785 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:25.792 [Information] CANRegisterAccess: Read value 0xF8 from register 0x0141 (simulated)
2025-07-05 18:17:25.799 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:25.805 [Information] CANRegisterAccess: Read value 0x10 from register 0x0141 (simulated)
2025-07-05 18:17:25.811 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:25.817 [Information] CANRegisterAccess: Read value 0x25 from register 0x0141 (simulated)
2025-07-05 18:17:25.817 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 18:17:25.818 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 18:17:25.819 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 18:17:25.825 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 18:17:25.826 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 18:17:25.832 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 18:17:25.832 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 18:17:25.833 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 18:17:25.839 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 18:17:25.839 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 18:17:25.840 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 18:17:25.846 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 18:17:25.846 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 18:17:25.852 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 18:17:25.852 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 18:17:25.858 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 18:17:25.858 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 18:17:25.865 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 18:17:25.865 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 18:17:25.871 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 18:17:25.871 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 18:17:25.877 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 18:17:25.877 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 18:17:25.883 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 18:17:25.883 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 18:17:25.888 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 18:17:25.888 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 18:17:25.894 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 18:17:25.894 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 18:17:25.900 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 18:17:25.900 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 18:17:25.906 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 18:17:25.906 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 18:17:25.912 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 18:17:25.912 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 18:17:25.918 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 18:17:25.918 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 18:17:25.924 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 18:17:25.924 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 18:17:25.929 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 18:17:25.929 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 18:17:25.935 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 18:17:25.935 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 18:17:25.941 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 18:17:25.941 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 18:17:25.942 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 18:17:25.949 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 18:17:25.949 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 18:17:25.950 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 18:17:25.950 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:25.956 [Information] CANRegisterAccess: Read value 0x88 from register 0x0141 (simulated)
2025-07-05 18:17:25.956 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 18:17:25.957 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 18:17:25.958 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 18:17:25.958 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:17:25.964 [Information] CANRegisterAccess: Read value 0xF1 from register 0x0140 (simulated)
2025-07-05 18:17:25.964 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 18:17:25.965 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:17:25.967 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:17:25.968 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:17:25.979 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 18:17:25.980 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 18:17:25.980 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 18:17:25.984 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 18:17:26.136 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 18:17:26.137 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 18:17:26.138 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 18:17:26.140 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:17:26.140 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:17:26.152 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:17:26.153 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 18:17:26.153 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 18:17:26.164 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 18:17:26.175 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 18:17:26.186 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 18:17:26.197 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 18:17:26.208 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:17:26.210 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:17:26.211 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:17:26.222 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:17:26.223 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 18:17:26.223 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 18:17:26.234 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 18:17:26.245 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 18:17:26.256 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 18:17:26.267 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 18:17:26.278 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 18:17:26.289 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:17:26.297 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:17:26.298 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:17:26.309 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:17:26.310 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 18:17:26.311 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:17:26.311 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:17:26.311 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 18:17:26.312 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:17:26.312 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 18:17:26.312 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 18:17:26.312 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 18:17:26.313 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 18:17:26.313 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 18:17:26.313 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 18:17:26.314 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 18:17:26.314 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 18:17:26.314 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 18:17:26.314 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 18:17:26.315 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 18:17:26.415 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:17:26.415 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 18:17:26.419 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 18:17:26.421 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:26.422 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 18:17:26.422 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 18:17:26.422 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:26.423 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 18:17:26.423 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 18:17:26.424 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:26.424 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 18:17:26.424 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 18:17:26.425 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:26.425 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 18:17:26.426 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 18:17:26.427 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 18:17:26.440 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 18:17:26.442 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 18:17:26.446 [Information] BackupService: Initializing backup service
2025-07-05 18:17:26.446 [Information] BackupService: Backup service initialized successfully
2025-07-05 18:17:26.447 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 18:17:26.447 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 18:17:26.449 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 18:17:26.489 [Information] BackupService: Compressing backup data
2025-07-05 18:17:26.498 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-05 18:17:26.499 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 18:17:26.499 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 18:17:26.500 [Information] BackupService: Compressing backup data
2025-07-05 18:17:26.501 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-05 18:17:26.502 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 18:17:26.502 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 18:17:26.502 [Information] BackupService: Compressing backup data
2025-07-05 18:17:26.503 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-05 18:17:26.504 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 18:17:26.504 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 18:17:26.504 [Information] BackupService: Compressing backup data
2025-07-05 18:17:26.505 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-07-05 18:17:26.506 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 18:17:26.506 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 18:17:26.506 [Information] BackupService: Compressing backup data
2025-07-05 18:17:26.507 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-05 18:17:26.508 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 18:17:26.508 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 18:17:26.508 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 18:17:26.509 [Information] BackupService: Compressing backup data
2025-07-05 18:17:26.511 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-07-05 18:17:26.511 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 18:17:26.511 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 18:17:26.514 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 18:17:26.517 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 18:17:26.526 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 18:17:26.614 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 18:17:26.615 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 18:17:26.617 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 18:17:26.618 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 18:17:26.618 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 18:17:26.619 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 18:17:26.620 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 18:17:26.628 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 18:17:26.628 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 18:17:26.644 [Information] LicensingService: Initializing licensing service
2025-07-05 18:17:26.701 [Information] LicensingService: License information loaded successfully
2025-07-05 18:17:26.704 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 18:17:26.704 [Information] App: Licensing service initialized successfully
2025-07-05 18:17:26.705 [Information] App: License status: Trial
2025-07-05 18:17:26.705 [Information] App: Trial period: 30 days remaining
2025-07-05 18:17:26.706 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 18:17:26.894 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 18:17:26.894 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 18:17:26.944 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 18:17:26.944 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 18:17:26.945 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 18:17:26.945 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 18:17:26.945 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 18:17:26.947 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 18:17:26.947 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 18:17:26.949 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 18:17:26.949 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:17:26.949 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:17:26.960 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:17:26.960 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 18:17:26.961 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 18:17:26.961 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:17:26.961 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:17:26.962 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 18:17:26.962 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:17:26.962 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 18:17:26.962 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 18:17:26.963 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 18:17:26.963 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 18:17:26.963 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 18:17:26.963 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 18:17:26.964 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 18:17:26.964 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 18:17:26.964 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 18:17:26.964 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 18:17:26.965 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 18:17:26.971 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 18:17:26.972 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 18:17:26.972 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 18:17:26.972 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:26.978 [Information] CANRegisterAccess: Read value 0x67 from register 0x0141 (simulated)
2025-07-05 18:17:26.978 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 18:17:26.979 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 18:17:26.979 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 18:17:26.985 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 18:17:26.985 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 18:17:26.992 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 18:17:26.993 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 18:17:26.993 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 18:17:26.999 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 18:17:26.999 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 18:17:27.000 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 18:17:27.006 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 18:17:27.006 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 18:17:27.013 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 18:17:27.013 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 18:17:27.020 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 18:17:27.020 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 18:17:27.027 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 18:17:27.027 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 18:17:27.034 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 18:17:27.034 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 18:17:27.041 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 18:17:27.042 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 18:17:27.048 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 18:17:27.048 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 18:17:27.055 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 18:17:27.055 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 18:17:27.062 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 18:17:27.062 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 18:17:27.068 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 18:17:27.068 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 18:17:27.074 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 18:17:27.075 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 18:17:27.081 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 18:17:27.082 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 18:17:27.088 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 18:17:27.088 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 18:17:27.095 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 18:17:27.095 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 18:17:27.102 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 18:17:27.102 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 18:17:27.109 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 18:17:27.110 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 18:17:27.116 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 18:17:27.116 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 18:17:27.117 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 18:17:27.123 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 18:17:27.123 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 18:17:27.124 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 18:17:27.124 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:27.130 [Information] CANRegisterAccess: Read value 0x07 from register 0x0141 (simulated)
2025-07-05 18:17:27.136 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:27.142 [Information] CANRegisterAccess: Read value 0xA9 from register 0x0141 (simulated)
2025-07-05 18:17:27.148 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:17:27.154 [Information] CANRegisterAccess: Read value 0x5E from register 0x0141 (simulated)
2025-07-05 18:17:27.154 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 18:17:27.155 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 18:17:27.155 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 18:17:27.155 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:17:27.161 [Information] CANRegisterAccess: Read value 0x07 from register 0x0140 (simulated)
2025-07-05 18:17:27.167 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:17:27.173 [Information] CANRegisterAccess: Read value 0xFA from register 0x0140 (simulated)
2025-07-05 18:17:27.173 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 18:17:27.174 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:17:27.174 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:17:27.175 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:17:27.186 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 18:17:27.187 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 18:17:27.187 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 18:17:27.187 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 18:17:27.338 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 18:17:27.338 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 18:17:27.339 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 18:17:27.339 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:17:27.340 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:17:27.351 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:17:27.352 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 18:17:27.352 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 18:17:27.363 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 18:17:27.374 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 18:17:27.385 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 18:17:27.396 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 18:17:27.407 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:17:27.407 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:17:27.408 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:17:27.419 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:17:27.419 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 18:17:27.420 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 18:17:27.431 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 18:17:27.442 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 18:17:27.453 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 18:17:27.464 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 18:17:27.475 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 18:17:27.486 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:17:27.486 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:17:27.487 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:17:27.498 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:17:27.498 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 18:17:27.499 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:17:27.499 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:17:27.499 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 18:17:27.500 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:17:27.500 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 18:17:27.500 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 18:17:27.500 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 18:17:27.501 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 18:17:27.501 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 18:17:27.501 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 18:17:27.501 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 18:17:27.501 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 18:17:27.502 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 18:17:27.502 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 18:17:27.502 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 18:17:27.602 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:17:27.602 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 18:17:27.603 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 18:17:27.603 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:27.604 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 18:17:27.604 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 18:17:27.604 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:27.605 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 18:17:27.605 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 18:17:27.605 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:27.606 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 18:17:27.606 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 18:17:27.606 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:17:27.606 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 18:17:27.607 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 18:17:27.657 [Information] BackupService: Initializing backup service
2025-07-05 18:17:27.658 [Information] BackupService: Backup service initialized successfully
2025-07-05 18:17:27.708 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 18:17:27.709 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 18:17:27.711 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 18:17:27.712 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 18:17:27.763 [Information] BackupService: Getting predefined backup categories
2025-07-05 18:17:27.815 [Information] MainViewModel: Services initialized successfully
2025-07-05 18:17:27.821 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 18:17:27.823 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 18:17:27.953 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 18:17:27.954 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-05 18:17:33.799 [Information] MainViewModel: Connecting to Vocom device DUMMY-00000000
2025-07-05 18:17:33.800 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 18:17:34.015 [Information] ECUCommunicationService: Vocom device connected: DUMMY-00000000
2025-07-05 18:17:34.016 [Information] MainViewModel: Vocom device DUMMY-00000000 connected
2025-07-05 18:17:34.016 [Information] ECUCommunicationService: Vocom device connected: DUMMY-00000000
2025-07-05 18:17:34.017 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 18:17:34.018 [Information] MainViewModel: Connected to Vocom device DUMMY-00000000
2025-07-05 18:17:34.026 [Information] MainViewModel: Scanning for ECUs
2025-07-05 18:17:34.033 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-05 18:17:34.034 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-05 18:17:34.533 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-05 18:17:34.833 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-05 18:17:35.134 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-05 18:17:35.436 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-05 18:17:35.437 [Information] MainViewModel: Found 10 ECU(s)
