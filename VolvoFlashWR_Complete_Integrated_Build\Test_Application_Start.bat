@echo off
title VolvoFlashWR - Application Start Test
echo === VolvoFlashWR Application Start Test ===
echo.

echo Testing application startup with skip download option...
echo.

REM Set environment variables to skip problematic downloads
set SKIP_VCREDIST_DOWNLOAD=true
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=true

echo Environment variables set:
echo - SKIP_VCREDIST_DOWNLOAD=true (skips VC++ library download)
echo - USE_PATCHED_IMPLEMENTATION=true
echo - VERBOSE_LOGGING=true
echo - PHOENIX_VOCOM_ENABLED=true
echo.

REM Change to Application directory
cd /d "Application"

echo Checking for required files...
if exist "VolvoFlashWR.Launcher.exe" (
    echo ✓ VolvoFlashWR.Launcher.exe found
) else (
    echo ✗ VolvoFlashWR.Launcher.exe NOT found
    goto :error
)

if exist "Bridge\VolvoFlashWR.VocomBridge.exe" (
    echo ✓ Bridge\VolvoFlashWR.VocomBridge.exe found
) else (
    echo ✗ Bridge\VolvoFlashWR.VocomBridge.exe NOT found
    goto :error
)

if exist "Libraries" (
    echo ✓ Libraries folder found
) else (
    echo ✗ Libraries folder NOT found
    goto :error
)

echo.
echo All required files found. Starting application...
echo Note: This will skip VC++ library downloads to avoid hanging
echo.

REM Start the application
"VolvoFlashWR.Launcher.exe"

echo.
echo Application closed.
goto :end

:error
echo.
echo ❌ Required files missing! Please ensure the application is properly deployed.
echo.

:end
pause
