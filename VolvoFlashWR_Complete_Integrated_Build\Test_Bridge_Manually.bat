@echo off
title Test Bridge Manually
echo === Testing Bridge Executable Manually ===
echo.

cd /d "Application"

echo Checking if bridge executable exists...
if exist "Bridge\VolvoFlashWR.VocomBridge.exe" (
    echo ✓ Bridge executable found
) else (
    echo ✗ Bridge executable NOT found
    pause
    exit /b 1
)

echo.
echo Testing bridge executable directly...
echo Note: This will fail because no pipe name is provided, but we can see if it starts
echo.

cd Bridge
echo Running: VolvoFlashWR.VocomBridge.exe TestPipe
"VolvoFlashWR.VocomBridge.exe" TestPipe

echo.
echo Bridge test completed. Check output above for any errors.
pause
