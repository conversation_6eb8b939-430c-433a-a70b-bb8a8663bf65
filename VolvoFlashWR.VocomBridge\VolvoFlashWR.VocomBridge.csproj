<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PlatformTarget>x86</PlatformTarget>
    <RuntimeIdentifier>win-x86</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
    <UseWindowsForms>false</UseWindowsForms>
    <UseWPF>false</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <!-- Core System Libraries -->
    <PackageReference Include="System.IO.Pipes" Version="4.3.0" />
    <PackageReference Include="System.Diagnostics.Process" Version="4.3.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    
    <!-- USB Communication for Vocom Hardware -->
    <PackageReference Include="HidSharp" Version="2.1.0" />
    <PackageReference Include="System.IO.Ports" Version="9.0.4" />
    <PackageReference Include="System.Management" Version="9.0.5" />
    
    <!-- Network Communication -->
    <PackageReference Include="System.Net.NetworkInformation" Version="4.3.0" />
    <PackageReference Include="InTheHand.Net.Bluetooth" Version="4.1.40" />
    
    <!-- Modern P/Invoke and Interop -->
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
  </ItemGroup>



</Project>
