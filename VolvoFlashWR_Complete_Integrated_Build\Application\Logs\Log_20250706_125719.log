Log started at 7/6/2025 12:57:19 PM
2025-07-06 12:57:19.806 [Information] LoggingService: Logging service initialized
2025-07-06 12:57:19.823 [Information] App: Starting integrated application initialization
2025-07-06 12:57:19.825 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 12:57:19.829 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 12:57:19.831 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 12:57:19.832 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 12:57:19.835 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 12:57:19.837 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 12:57:19.840 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 12:57:19.847 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:57:19.850 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.853 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.854 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 12:57:19.857 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:57:19.859 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.862 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.862 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 12:57:19.865 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:57:19.868 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.871 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.871 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 12:57:19.875 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:57:19.878 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.881 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:57:19.883 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 12:57:19.889 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 12:57:19.891 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 12:57:19.892 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 12:57:19.896 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 12:57:19.896 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 12:57:19.898 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 12:57:19.898 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 12:57:19.899 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 12:57:19.902 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 12:57:19.903 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 12:57:19.904 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 12:57:19.904 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 12:57:19.905 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 12:57:19.905 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 12:57:19.913 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 12:57:19.913 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 12:57:19.914 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 12:57:19.920 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 12:57:19.920 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 12:57:19.922 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 12:57:19.923 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 12:57:19.929 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 12:57:19.931 [Information] LibraryExtractor: Copying system libraries
2025-07-06 12:57:19.938 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 12:57:19.946 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 12:57:44.484 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 12:58:30.983 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 12:59:13.288 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 12:59:55.727 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 13:00:40.257 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 13:01:21.445 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 13:02:07.280 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 13:02:07.281 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 13:02:07.281 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 13:02:07.281 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 13:02:07.282 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 13:02:07.282 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 13:02:07.288 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 13:02:07.290 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 13:02:07.292 [Information] DependencyManager: Initializing dependency manager
2025-07-06 13:02:07.293 [Information] DependencyManager: Setting up library search paths
2025-07-06 13:02:07.295 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 13:02:07.295 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 13:02:07.296 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 13:02:07.296 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 13:02:07.298 [Information] DependencyManager: Verifying required directories
2025-07-06 13:02:07.298 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 13:02:07.299 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 13:02:07.299 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 13:02:07.300 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 13:02:07.303 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 13:02:07.312 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 13:02:07.315 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 13:02:07.320 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 13:02:07.325 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 13:02:07.328 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 13:02:07.329 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 13:02:07.330 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 13:02:07.331 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 13:02:07.333 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 13:02:07.336 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 13:02:07.337 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 13:02:07.339 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 13:02:07.340 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 13:02:07.341 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 13:02:07.344 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 13:02:07.345 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 13:02:07.345 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 13:02:07.346 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 13:02:07.347 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 13:02:07.348 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 13:02:07.349 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 13:02:07.349 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 13:02:07.352 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 13:02:07.353 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 13:02:07.355 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 13:02:07.356 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 13:02:07.357 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 13:02:07.358 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 13:02:07.358 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 13:02:07.360 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 13:02:07.361 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 13:02:07.361 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 13:02:07.362 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 13:02:07.363 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 13:02:07.364 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 13:02:07.365 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 13:02:07.366 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 13:02:07.368 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 13:02:07.369 [Information] DependencyManager: Setting up environment variables
2025-07-06 13:02:07.370 [Information] DependencyManager: Environment variables configured
2025-07-06 13:02:07.371 [Information] DependencyManager: Verifying library loading status
2025-07-06 13:02:07.726 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 13:02:07.727 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 13:02:07.727 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 13:02:07.730 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 13:02:07.732 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 13:02:07.738 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 13:02:07.740 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 13:02:07.741 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 13:02:07.741 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 13:02:07.743 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 13:02:07.743 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 13:02:07.744 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 13:02:07.744 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 13:02:07.744 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 13:02:07.745 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 13:02:07.745 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 13:02:07.745 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 13:02:07.746 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 13:02:07.746 [Information] App: Integrated startup completed successfully
2025-07-06 13:02:07.749 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 13:02:07.769 [Information] App: Initializing application services
2025-07-06 13:02:07.771 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 13:02:07.771 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 13:02:07.816 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 13:02:07.817 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 13:02:07.818 [Information] App: Configuration service initialized successfully
2025-07-06 13:02:07.820 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 13:02:07.821 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 13:02:07.827 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 13:02:07.828 [Information] App: Final useDummyImplementations value: False
2025-07-06 13:02:07.828 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 13:02:07.830 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 13:02:07.849 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 13:02:07.852 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 13:02:07.852 [Information] App: usePatchedImplementation flag is: True
2025-07-06 13:02:07.853 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 13:02:07.853 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 13:02:07.853 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 13:02:07.854 [Information] App: verboseLogging flag is: True
2025-07-06 13:02:07.856 [Information] App: Verifying real hardware requirements...
2025-07-06 13:02:07.857 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 13:02:07.857 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 13:02:07.857 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 13:02:07.858 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 13:02:07.858 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 13:02:07.859 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 13:02:07.859 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 13:02:07.859 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 13:02:07.871 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 13:02:07.873 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 13:02:07.876 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 13:02:07.878 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 13:02:07.879 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 13:02:07.879 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 13:02:07.880 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 13:02:07.880 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 13:02:07.880 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 13:02:07.881 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 13:02:07.881 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 13:02:07.881 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 13:02:07.885 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 13:02:07.885 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 13:02:07.887 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 13:02:07.888 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 13:02:07.889 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 13:02:07.889 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 13:02:07.891 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 13:02:07.891 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 13:02:07.892 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 13:02:07.894 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 13:02:07.897 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 13:02:07.907 [Information] VocomArchitectureBridge: Started bridge process with PID 8000
2025-07-06 13:02:08.909 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 13:02:08.913 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 13:02:09.019 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 13:02:09.022 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 13:02:09.022 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 13:02:09.023 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 13:02:09.023 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 13:02:09.023 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 13:02:09.024 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 13:02:09.024 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 13:02:09.024 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 13:02:09.025 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 13:02:09.025 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 13:02:09.083 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 13:02:09.170 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 13:02:09.172 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 13:02:09.173 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 13:02:09.177 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 13:02:09.191 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:09.191 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 13:02:09.196 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 13:02:09.198 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 13:02:09.199 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 13:02:09.211 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 13:02:09.215 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 13:02:09.221 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 13:02:09.225 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 13:02:09.229 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 13:02:09.238 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 13:02:09.242 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 13:02:09.243 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:09.246 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 13:02:09.246 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 13:02:09.247 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:09.249 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 13:02:09.250 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 13:02:09.251 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:09.254 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 13:02:09.255 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 13:02:09.255 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:09.257 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 13:02:09.257 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 13:02:09.258 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:09.258 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 13:02:09.261 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 13:02:09.263 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:09.263 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 13:02:09.264 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 13:02:09.264 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:09.265 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 13:02:09.265 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 13:02:09.265 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:09.266 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 13:02:09.266 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 13:02:09.267 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:09.267 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 13:02:09.269 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 13:02:09.269 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 13:02:09.271 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 13:02:09.274 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 13:02:09.274 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 13:02:09.275 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 13:02:09.276 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 13:02:09.277 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 13:02:10.279 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 13:02:10.279 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 13:02:10.280 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 13:02:10.281 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 13:02:10.281 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 13:02:10.281 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 13:02:10.284 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 13:02:10.284 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 13:02:10.287 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 13:02:10.287 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 13:02:10.288 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 13:02:10.288 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:10.288 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 13:02:10.288 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 13:02:10.289 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:10.289 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 13:02:10.290 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 13:02:10.290 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:10.290 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 13:02:10.290 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 13:02:10.291 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:10.291 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 13:02:10.291 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 13:02:10.292 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:10.292 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 13:02:10.292 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 13:02:10.293 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:10.293 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 13:02:10.293 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 13:02:10.294 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:10.294 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 13:02:10.294 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 13:02:10.295 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:10.296 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 13:02:10.296 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 13:02:10.296 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:10.297 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 13:02:10.297 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 13:02:10.297 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 13:02:10.297 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 13:02:10.299 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 13:02:10.299 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 13:02:10.300 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 13:02:10.300 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 13:02:10.300 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 13:02:12.300 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 13:02:12.301 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 13:02:12.302 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 13:02:12.302 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 13:02:12.304 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 13:02:12.304 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 13:02:12.306 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 13:02:12.306 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 13:02:12.307 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 13:02:12.308 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 13:02:12.308 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 13:02:12.309 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:12.309 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 13:02:12.309 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 13:02:12.309 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:12.310 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 13:02:12.310 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 13:02:12.310 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:12.311 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 13:02:12.311 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 13:02:12.311 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:12.311 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 13:02:12.312 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 13:02:12.312 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 13:02:12.312 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 13:02:12.313 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 13:02:12.313 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:12.314 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 13:02:12.314 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 13:02:12.314 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:12.314 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 13:02:12.315 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 13:02:12.315 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:12.315 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 13:02:12.316 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 13:02:12.316 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 13:02:12.316 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 13:02:12.317 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 13:02:12.317 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 13:02:12.317 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 13:02:12.321 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 13:02:12.321 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 13:02:12.322 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 13:02:12.322 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 13:02:12.322 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 13:02:15.322 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 13:02:15.323 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 13:02:15.324 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 13:02:15.326 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 13:02:15.827 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 13:02:15.828 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 13:02:15.829 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 13:02:15.829 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 13:02:15.833 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 13:02:15.835 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 13:02:15.839 [Information] BackupService: Initializing backup service
2025-07-06 13:02:15.840 [Information] BackupService: Backup service initialized successfully
2025-07-06 13:02:15.840 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 13:02:15.840 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 13:02:15.844 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 13:02:15.880 [Information] BackupService: Compressing backup data
2025-07-06 13:02:15.891 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-06 13:02:15.893 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 13:02:15.893 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 13:02:15.894 [Information] BackupService: Compressing backup data
2025-07-06 13:02:15.895 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-07-06 13:02:15.896 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 13:02:15.896 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 13:02:15.896 [Information] BackupService: Compressing backup data
2025-07-06 13:02:15.898 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-06 13:02:15.898 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 13:02:15.898 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 13:02:15.899 [Information] BackupService: Compressing backup data
2025-07-06 13:02:15.900 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-06 13:02:15.900 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 13:02:15.901 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 13:02:15.901 [Information] BackupService: Compressing backup data
2025-07-06 13:02:15.902 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (445 bytes)
2025-07-06 13:02:15.903 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 13:02:15.903 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 13:02:15.905 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 13:02:15.906 [Information] BackupService: Compressing backup data
2025-07-06 13:02:15.907 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-06 13:02:15.907 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 13:02:15.907 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 13:02:15.909 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 13:02:15.913 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 13:02:15.915 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 13:02:16.011 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 13:02:16.012 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 13:02:16.014 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 13:02:16.014 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 13:02:16.014 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 13:02:16.016 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 13:02:16.017 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 13:02:16.022 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 13:02:16.022 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 13:02:16.034 [Information] LicensingService: Initializing licensing service
2025-07-06 13:02:16.097 [Information] LicensingService: License information loaded successfully
2025-07-06 13:02:16.108 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 13:02:16.109 [Information] App: Licensing service initialized successfully
2025-07-06 13:02:16.109 [Information] App: License status: Trial
2025-07-06 13:02:16.110 [Information] App: Trial period: 29 days remaining
2025-07-06 13:02:16.111 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 13:02:16.329 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 13:02:16.329 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 13:02:16.330 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 13:02:16.330 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 13:02:16.381 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 13:02:16.882 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 13:02:16.933 [Information] BackupService: Initializing backup service
2025-07-06 13:02:16.934 [Information] BackupService: Backup service initialized successfully
2025-07-06 13:02:16.985 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 13:02:16.986 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 13:02:16.989 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 13:02:16.989 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 13:02:17.042 [Information] BackupService: Getting predefined backup categories
2025-07-06 13:02:17.094 [Information] MainViewModel: Services initialized successfully
2025-07-06 13:02:17.098 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 13:02:17.100 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 13:02:17.139 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 13:02:17.140 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 13:02:17.141 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 13:02:58.566 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 13:02:58.567 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 13:02:58.575 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 13:02:58.576 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 13:02:58.577 [Information] MainViewModel: Found 0 Vocom device(s)
