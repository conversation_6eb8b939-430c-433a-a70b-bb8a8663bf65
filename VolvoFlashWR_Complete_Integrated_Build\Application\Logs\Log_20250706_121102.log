Log started at 7/6/2025 12:11:02 PM
2025-07-06 12:11:02.033 [Information] LoggingService: Logging service initialized
2025-07-06 12:11:02.046 [Information] App: Starting integrated application initialization
2025-07-06 12:11:02.048 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 12:11:02.050 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 12:11:02.051 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 12:11:02.052 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 12:11:02.054 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 12:11:02.056 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 12:11:02.060 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 12:11:02.067 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:11:02.069 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.071 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.072 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 12:11:02.075 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:11:02.078 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.080 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.080 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 12:11:02.083 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:11:02.085 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.088 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.088 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 12:11:02.092 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 12:11:02.095 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.098 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 12:11:02.098 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 12:11:02.100 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 12:11:02.102 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 12:11:02.103 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 12:11:02.117 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 12:11:02.118 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 12:11:02.127 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 12:11:02.127 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 12:11:02.128 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 12:11:02.189 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 12:11:02.190 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 12:11:02.241 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 12:11:02.241 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 12:11:02.242 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 12:11:02.242 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 12:11:02.249 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 12:11:02.250 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 12:11:02.251 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 12:11:02.257 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 12:11:02.258 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 12:11:02.260 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 12:11:02.263 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 12:11:02.266 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 12:11:02.268 [Information] LibraryExtractor: Copying system libraries
2025-07-06 12:11:02.273 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 12:11:02.281 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 12:11:46.695 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 12:12:38.672 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 12:13:25.880 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 12:14:07.006 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 12:14:54.008 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 12:15:37.448 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 12:16:22.282 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 12:16:22.283 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 12:16:22.283 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 12:16:22.284 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 12:16:22.284 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 12:16:22.285 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 12:16:22.289 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 12:16:22.292 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 12:16:22.294 [Information] DependencyManager: Initializing dependency manager
2025-07-06 12:16:22.295 [Information] DependencyManager: Setting up library search paths
2025-07-06 12:16:22.296 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 12:16:22.296 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 12:16:22.297 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 12:16:22.297 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 12:16:22.300 [Information] DependencyManager: Verifying required directories
2025-07-06 12:16:22.300 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 12:16:22.301 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 12:16:22.301 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 12:16:22.302 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 12:16:22.303 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 12:16:22.312 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 12:16:22.314 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 12:16:22.317 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 12:16:22.319 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 12:16:22.320 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 12:16:22.321 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 12:16:22.325 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 12:16:22.326 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 12:16:22.327 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 12:16:22.377 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 12:16:22.482 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 12:16:22.482 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 12:16:22.581 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 12:16:22.581 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 12:16:22.582 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 12:16:22.672 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 12:16:22.673 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 12:16:22.750 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 12:16:22.750 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 12:16:22.751 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 12:16:23.133 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 12:16:23.133 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 12:16:23.418 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 12:16:23.418 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 12:16:23.419 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 12:16:23.529 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 12:16:23.530 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 12:16:23.645 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 12:16:23.646 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 12:16:23.647 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 12:16:23.718 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 12:16:23.718 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 12:16:23.719 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 12:16:23.720 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 12:16:23.721 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 12:16:23.722 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 12:16:23.722 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 12:16:23.723 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 12:16:23.725 [Information] DependencyManager: Setting up environment variables
2025-07-06 12:16:23.725 [Information] DependencyManager: Environment variables configured
2025-07-06 12:16:23.727 [Information] DependencyManager: Verifying library loading status
2025-07-06 12:16:24.088 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 12:16:24.091 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 12:16:24.091 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 12:16:24.095 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 12:16:24.096 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 12:16:24.101 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 12:16:24.104 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 12:16:24.105 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 12:16:24.105 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 12:16:24.107 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 12:16:24.108 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 12:16:24.109 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 12:16:24.109 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 12:16:24.109 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 12:16:24.109 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 12:16:24.110 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 12:16:24.110 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 12:16:24.110 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 12:16:24.111 [Information] App: Integrated startup completed successfully
2025-07-06 12:16:24.113 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 12:16:24.130 [Information] App: Initializing application services
2025-07-06 12:16:24.131 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 12:16:24.132 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 12:16:24.184 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 12:16:24.185 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 12:16:24.186 [Information] App: Configuration service initialized successfully
2025-07-06 12:16:24.188 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 12:16:24.189 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 12:16:24.198 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 12:16:24.199 [Information] App: Final useDummyImplementations value: False
2025-07-06 12:16:24.199 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 12:16:24.202 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 12:16:24.220 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 12:16:24.221 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 12:16:24.221 [Information] App: usePatchedImplementation flag is: True
2025-07-06 12:16:24.222 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 12:16:24.222 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 12:16:24.222 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 12:16:24.223 [Information] App: verboseLogging flag is: True
2025-07-06 12:16:24.226 [Information] App: Verifying real hardware requirements...
2025-07-06 12:16:24.226 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 12:16:24.227 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 12:16:24.227 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 12:16:24.227 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 12:16:24.228 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 12:16:24.228 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 12:16:24.229 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 12:16:24.229 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 12:16:24.240 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 12:16:24.243 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 12:16:24.245 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 12:16:24.248 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 12:16:24.248 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 12:16:24.248 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 12:16:24.249 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 12:16:24.249 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 12:16:24.249 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 12:16:24.250 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 12:16:24.250 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 12:16:24.250 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 12:16:24.251 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 12:16:24.252 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 12:16:24.254 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 12:16:24.255 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 12:16:24.255 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 12:16:24.256 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 12:16:24.258 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 12:16:24.259 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 12:16:24.260 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 12:16:24.262 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 12:16:24.266 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 12:16:24.279 [Information] VocomArchitectureBridge: Started bridge process with PID 2816
2025-07-06 12:16:25.280 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 12:16:25.285 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 12:16:25.398 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 12:16:25.399 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 12:16:25.400 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 12:16:25.400 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 12:16:25.400 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 12:16:25.401 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 12:16:25.401 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 12:16:25.401 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 12:16:25.402 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 12:16:25.402 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 12:16:25.402 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 12:16:25.453 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 12:16:25.863 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 12:16:25.864 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 12:16:25.864 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 12:16:25.868 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 12:16:25.872 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:25.872 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 12:16:25.877 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 12:16:25.879 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 12:16:25.880 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 12:16:25.883 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 12:16:25.887 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 12:16:25.893 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 12:16:25.897 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 12:16:25.901 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 12:16:25.910 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 12:16:25.913 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 12:16:25.914 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:25.916 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 12:16:25.917 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 12:16:25.917 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:25.919 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 12:16:25.920 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 12:16:25.920 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:25.922 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 12:16:25.922 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 12:16:25.923 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:25.925 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 12:16:25.926 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 12:16:25.927 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:25.927 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 12:16:25.930 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 12:16:25.932 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:25.932 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 12:16:25.932 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 12:16:25.933 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:25.933 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 12:16:25.934 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 12:16:25.934 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:25.935 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 12:16:25.935 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 12:16:25.935 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:25.936 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 12:16:25.936 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 12:16:25.936 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 12:16:25.938 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 12:16:25.940 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 12:16:25.940 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 12:16:25.941 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 12:16:25.942 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 12:16:25.943 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 12:16:26.944 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 12:16:26.944 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 12:16:26.945 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 12:16:26.945 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 12:16:26.946 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 12:16:26.946 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 12:16:26.947 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 12:16:26.948 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 12:16:26.949 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 12:16:26.950 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 12:16:26.950 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 12:16:26.950 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:26.951 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 12:16:26.951 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 12:16:26.952 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:26.952 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 12:16:26.952 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 12:16:26.953 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:26.953 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 12:16:26.954 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 12:16:26.954 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:26.954 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 12:16:26.955 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 12:16:26.955 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:26.957 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 12:16:26.961 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 12:16:26.961 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:26.963 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 12:16:26.963 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 12:16:26.971 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:26.972 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 12:16:26.972 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 12:16:26.973 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:26.973 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 12:16:26.975 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 12:16:26.977 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:26.977 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 12:16:26.977 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 12:16:26.977 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 12:16:26.978 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 12:16:26.987 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 12:16:26.987 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 12:16:26.987 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 12:16:26.988 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 12:16:26.988 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 12:16:28.988 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 12:16:28.988 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 12:16:28.989 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 12:16:28.989 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 12:16:28.989 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 12:16:28.990 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 12:16:28.991 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 12:16:28.991 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 12:16:28.992 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 12:16:28.993 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 12:16:28.995 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 12:16:28.995 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:28.996 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 12:16:28.996 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 12:16:29.001 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:29.002 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 12:16:29.003 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 12:16:29.003 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:29.004 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 12:16:29.004 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 12:16:29.004 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:29.005 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 12:16:29.005 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 12:16:29.005 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 12:16:29.005 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 12:16:29.006 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 12:16:29.006 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:29.006 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 12:16:29.007 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 12:16:29.007 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:29.007 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 12:16:29.008 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 12:16:29.010 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:29.010 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 12:16:29.010 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 12:16:29.011 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 12:16:29.011 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 12:16:29.011 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 12:16:29.012 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 12:16:29.012 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 12:16:29.018 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 12:16:29.019 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 12:16:29.019 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 12:16:29.019 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 12:16:29.020 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 12:16:32.021 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 12:16:32.021 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 12:16:32.023 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 12:16:32.024 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 12:16:32.526 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 12:16:32.526 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 12:16:32.528 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 12:16:32.528 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 12:16:32.532 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 12:16:32.534 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 12:16:32.539 [Information] BackupService: Initializing backup service
2025-07-06 12:16:32.539 [Information] BackupService: Backup service initialized successfully
2025-07-06 12:16:32.540 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 12:16:32.540 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 12:16:32.544 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 12:16:32.574 [Information] BackupService: Compressing backup data
2025-07-06 12:16:32.584 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-07-06 12:16:32.585 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 12:16:32.586 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 12:16:32.586 [Information] BackupService: Compressing backup data
2025-07-06 12:16:32.587 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (448 bytes)
2025-07-06 12:16:32.588 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 12:16:32.588 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 12:16:32.589 [Information] BackupService: Compressing backup data
2025-07-06 12:16:32.590 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (444 bytes)
2025-07-06 12:16:32.590 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 12:16:32.590 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 12:16:32.591 [Information] BackupService: Compressing backup data
2025-07-06 12:16:32.592 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-06 12:16:32.592 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 12:16:32.593 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 12:16:32.594 [Information] BackupService: Compressing backup data
2025-07-06 12:16:32.595 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-06 12:16:32.596 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 12:16:32.596 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 12:16:32.596 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 12:16:32.597 [Information] BackupService: Compressing backup data
2025-07-06 12:16:32.598 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-06 12:16:32.599 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 12:16:32.599 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 12:16:32.603 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 12:16:32.606 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 12:16:32.609 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 12:16:32.687 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 12:16:32.687 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 12:16:32.689 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 12:16:32.690 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 12:16:32.690 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 12:16:32.692 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 12:16:32.693 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 12:16:32.698 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 12:16:32.698 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 12:16:32.712 [Information] LicensingService: Initializing licensing service
2025-07-06 12:16:32.773 [Information] LicensingService: License information loaded successfully
2025-07-06 12:16:32.775 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 12:16:32.776 [Information] App: Licensing service initialized successfully
2025-07-06 12:16:32.776 [Information] App: License status: Trial
2025-07-06 12:16:32.777 [Information] App: Trial period: 29 days remaining
2025-07-06 12:16:32.778 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 12:16:32.960 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 12:16:32.960 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 12:16:32.961 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 12:16:32.961 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 12:16:33.011 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 12:16:33.511 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 12:16:33.562 [Information] BackupService: Initializing backup service
2025-07-06 12:16:33.563 [Information] BackupService: Backup service initialized successfully
2025-07-06 12:16:33.615 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 12:16:33.615 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 12:16:33.616 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 12:16:33.617 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 12:16:33.668 [Information] BackupService: Getting predefined backup categories
2025-07-06 12:16:33.721 [Information] MainViewModel: Services initialized successfully
2025-07-06 12:16:33.723 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 12:16:33.725 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 12:16:33.742 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 12:16:33.743 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 12:16:33.745 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 12:22:43.442 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 12:22:43.443 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 12:22:43.479 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 12:22:43.480 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 12:22:43.480 [Information] MainViewModel: Found 0 Vocom device(s)
