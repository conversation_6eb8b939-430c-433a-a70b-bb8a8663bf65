Log started at 7/6/2025 11:51:33 AM
2025-07-06 11:51:33.310 [Information] LoggingService: Logging service initialized
2025-07-06 11:51:33.322 [Information] App: Starting integrated application initialization
2025-07-06 11:51:33.324 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 11:51:33.327 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 11:51:33.329 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 11:51:33.329 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 11:51:33.331 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 11:51:33.333 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 11:51:33.337 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 11:51:33.346 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:51:33.348 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.351 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.352 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 11:51:33.356 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:51:33.359 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.361 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.362 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:51:33.365 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:51:33.368 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.371 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.372 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:51:33.376 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:51:33.379 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.381 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:51:33.382 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:51:33.384 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 11:51:33.387 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 11:51:33.388 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 11:51:33.390 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 11:51:33.390 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 11:51:33.391 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 11:51:33.392 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 11:51:33.392 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 11:51:33.393 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 11:51:33.393 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 11:51:33.394 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 11:51:33.394 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:51:33.394 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:51:33.395 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:51:33.402 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 11:51:33.403 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 11:51:33.404 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 11:51:33.411 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 11:51:33.412 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 11:51:33.413 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 11:51:33.415 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 11:51:33.419 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 11:51:33.424 [Information] LibraryExtractor: Copying system libraries
2025-07-06 11:51:33.430 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 11:51:33.440 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 11:52:01.594 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 11:52:50.864 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 11:53:35.581 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 11:54:18.766 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:55:03.536 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:55:48.900 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:56:34.442 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 11:56:34.442 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 11:56:34.443 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 11:56:34.443 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 11:56:34.444 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 11:56:34.444 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 11:56:34.447 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 11:56:34.449 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 11:56:34.451 [Information] DependencyManager: Initializing dependency manager
2025-07-06 11:56:34.452 [Information] DependencyManager: Setting up library search paths
2025-07-06 11:56:34.453 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:56:34.453 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 11:56:34.454 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 11:56:34.454 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 11:56:34.456 [Information] DependencyManager: Verifying required directories
2025-07-06 11:56:34.456 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:56:34.457 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 11:56:34.457 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 11:56:34.458 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 11:56:34.459 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 11:56:34.467 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 11:56:34.469 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 11:56:34.472 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 11:56:34.476 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 11:56:34.477 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 11:56:34.478 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:56:34.479 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:56:34.479 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:56:34.481 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 11:56:34.482 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 11:56:34.483 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 11:56:34.483 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 11:56:34.484 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 11:56:34.484 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 11:56:34.485 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 11:56:34.486 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 11:56:34.486 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 11:56:34.487 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 11:56:34.487 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 11:56:34.488 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 11:56:34.489 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 11:56:34.489 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 11:56:34.490 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 11:56:34.492 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 11:56:34.493 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 11:56:34.494 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 11:56:34.494 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 11:56:34.495 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 11:56:34.495 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 11:56:34.496 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 11:56:34.497 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 11:56:34.497 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 11:56:34.498 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 11:56:34.499 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 11:56:34.500 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 11:56:34.500 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 11:56:34.501 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 11:56:34.503 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 11:56:34.503 [Information] DependencyManager: Setting up environment variables
2025-07-06 11:56:34.504 [Information] DependencyManager: Environment variables configured
2025-07-06 11:56:34.506 [Information] DependencyManager: Verifying library loading status
2025-07-06 11:56:34.836 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 11:56:34.837 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 11:56:34.838 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 11:56:34.840 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 11:56:34.843 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 11:56:34.847 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 11:56:34.849 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 11:56:34.850 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 11:56:34.850 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 11:56:34.851 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 11:56:34.852 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 11:56:34.852 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:56:34.853 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 11:56:34.853 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 11:56:34.853 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 11:56:34.853 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 11:56:34.854 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:56:34.854 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 11:56:34.854 [Information] App: Integrated startup completed successfully
2025-07-06 11:56:34.858 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 11:56:34.870 [Information] App: Initializing application services
2025-07-06 11:56:34.872 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 11:56:34.873 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 11:56:34.916 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 11:56:34.917 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 11:56:34.918 [Information] App: Configuration service initialized successfully
2025-07-06 11:56:34.919 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 11:56:34.920 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 11:56:34.925 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 11:56:34.926 [Information] App: Final useDummyImplementations value: False
2025-07-06 11:56:34.926 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 11:56:34.928 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 11:56:34.943 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 11:56:34.944 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 11:56:34.944 [Information] App: usePatchedImplementation flag is: True
2025-07-06 11:56:34.945 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 11:56:34.945 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 11:56:34.946 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 11:56:34.946 [Information] App: verboseLogging flag is: True
2025-07-06 11:56:34.949 [Information] App: Verifying real hardware requirements...
2025-07-06 11:56:34.949 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 11:56:34.950 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 11:56:34.950 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 11:56:34.950 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 11:56:34.951 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 11:56:34.951 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 11:56:34.951 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 11:56:34.952 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 11:56:34.963 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 11:56:34.965 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 11:56:34.968 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 11:56:34.971 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 11:56:34.971 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 11:56:34.971 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 11:56:34.972 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 11:56:34.972 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 11:56:34.972 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 11:56:34.973 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 11:56:34.973 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 11:56:34.973 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 11:56:34.976 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 11:56:34.976 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 11:56:34.978 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 11:56:34.979 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 11:56:34.979 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 11:56:34.980 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 11:56:34.981 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 11:56:34.982 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 11:56:34.983 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 11:56:34.984 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 11:56:34.988 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 11:56:35.063 [Information] VocomArchitectureBridge: Started bridge process with PID 15508
2025-07-06 11:56:36.064 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 11:56:36.068 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 11:56:36.163 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 11:56:36.164 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 11:56:36.164 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 11:56:36.165 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 11:56:36.165 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 11:56:36.165 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 11:56:36.166 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 11:56:36.166 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 11:56:36.166 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 11:56:36.166 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 11:56:36.167 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 11:56:36.212 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:56:36.228 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:56:36.229 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:56:36.229 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 11:56:36.232 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 11:56:36.236 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:36.237 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 11:56:36.241 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 11:56:36.244 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 11:56:36.244 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 11:56:36.247 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 11:56:36.250 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 11:56:36.253 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 11:56:36.256 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 11:56:36.262 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 11:56:36.271 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:56:36.276 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:56:36.277 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:36.280 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:56:36.281 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:56:36.281 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:36.283 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:56:36.284 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:56:36.284 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:36.287 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:56:36.287 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:56:36.288 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:36.290 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:56:36.291 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:56:36.293 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:36.294 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 11:56:36.298 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 11:56:36.299 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:36.300 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 11:56:36.300 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 11:56:36.301 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:36.301 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 11:56:36.301 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 11:56:36.302 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:36.302 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 11:56:36.303 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 11:56:36.303 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:36.304 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 11:56:36.304 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 11:56:36.304 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 11:56:36.306 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:56:36.310 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:56:36.311 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:56:36.312 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 11:56:36.312 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 11:56:36.313 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 11:56:37.314 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 11:56:37.314 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 11:56:37.315 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 11:56:37.316 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 11:56:37.316 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 11:56:37.316 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 11:56:37.318 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 11:56:37.318 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 11:56:37.319 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 11:56:37.319 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:56:37.320 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:56:37.320 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:37.320 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:56:37.320 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:56:37.321 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:37.321 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:56:37.321 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:56:37.322 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:37.322 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:56:37.322 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:56:37.323 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:37.323 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:56:37.323 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:56:37.323 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:37.324 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 11:56:37.325 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 11:56:37.325 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:37.326 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 11:56:37.328 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 11:56:37.328 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:37.329 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 11:56:37.329 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 11:56:37.329 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:37.330 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 11:56:37.330 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 11:56:37.330 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:37.330 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 11:56:37.331 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 11:56:37.331 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 11:56:37.331 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:56:37.333 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:56:37.333 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:56:37.334 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 11:56:37.334 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 11:56:37.334 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 11:56:39.334 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 11:56:39.335 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 11:56:39.335 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 11:56:39.336 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 11:56:39.336 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 11:56:39.337 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 11:56:39.337 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 11:56:39.338 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 11:56:39.339 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 11:56:39.339 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:56:39.339 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:56:39.339 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:39.340 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:56:39.340 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:56:39.340 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:39.341 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:56:39.341 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:56:39.343 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:39.344 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:56:39.344 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:56:39.344 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:39.344 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:56:39.345 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:56:39.345 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:56:39.345 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 11:56:39.346 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 11:56:39.346 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:39.346 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 11:56:39.346 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 11:56:39.347 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:39.347 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 11:56:39.347 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 11:56:39.348 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:39.348 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 11:56:39.348 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 11:56:39.348 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:56:39.349 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 11:56:39.349 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 11:56:39.349 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 11:56:39.349 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:56:39.351 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:56:39.351 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:56:39.352 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 11:56:39.352 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 11:56:39.352 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 11:56:42.352 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 11:56:42.352 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 11:56:42.354 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 11:56:42.355 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 11:56:42.856 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 11:56:42.856 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 11:56:42.857 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 11:56:42.858 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 11:56:42.862 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 11:56:42.864 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 11:56:42.867 [Information] BackupService: Initializing backup service
2025-07-06 11:56:42.867 [Information] BackupService: Backup service initialized successfully
2025-07-06 11:56:42.867 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 11:56:42.868 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 11:56:42.870 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 11:56:42.902 [Information] BackupService: Compressing backup data
2025-07-06 11:56:42.913 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 11:56:42.914 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 11:56:42.914 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 11:56:42.915 [Information] BackupService: Compressing backup data
2025-07-06 11:56:42.916 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-06 11:56:42.917 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 11:56:42.917 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 11:56:42.918 [Information] BackupService: Compressing backup data
2025-07-06 11:56:42.918 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-07-06 11:56:42.919 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 11:56:42.919 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 11:56:42.920 [Information] BackupService: Compressing backup data
2025-07-06 11:56:42.921 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-06 11:56:42.921 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 11:56:42.921 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 11:56:42.922 [Information] BackupService: Compressing backup data
2025-07-06 11:56:42.923 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-06 11:56:42.923 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 11:56:42.923 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 11:56:42.924 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 11:56:42.924 [Information] BackupService: Compressing backup data
2025-07-06 11:56:42.925 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-06 11:56:42.926 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 11:56:42.926 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 11:56:42.929 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 11:56:42.932 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 11:56:42.935 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 11:56:43.144 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 11:56:43.145 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 11:56:43.147 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 11:56:43.147 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 11:56:43.147 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 11:56:43.149 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 11:56:43.150 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 11:56:43.157 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 11:56:43.157 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 11:56:43.168 [Information] LicensingService: Initializing licensing service
2025-07-06 11:56:43.210 [Information] LicensingService: License information loaded successfully
2025-07-06 11:56:43.213 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 11:56:43.214 [Information] App: Licensing service initialized successfully
2025-07-06 11:56:43.214 [Information] App: License status: Trial
2025-07-06 11:56:43.214 [Information] App: Trial period: 29 days remaining
2025-07-06 11:56:43.215 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 11:56:43.387 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 11:56:43.387 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 11:56:43.388 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 11:56:43.388 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 11:56:43.439 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 11:56:43.939 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 11:56:43.990 [Information] BackupService: Initializing backup service
2025-07-06 11:56:43.991 [Information] BackupService: Backup service initialized successfully
2025-07-06 11:56:44.042 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 11:56:44.043 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 11:56:44.047 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 11:56:44.047 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 11:56:44.099 [Information] BackupService: Getting predefined backup categories
2025-07-06 11:56:44.151 [Information] MainViewModel: Services initialized successfully
2025-07-06 11:56:44.153 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 11:56:44.155 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:56:44.195 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:56:44.195 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:56:44.196 [Information] MainViewModel: Found 0 Vocom device(s)
