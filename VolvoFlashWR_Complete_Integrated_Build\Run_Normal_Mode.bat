@echo off
title VolvoFlashWR - Normal Mode
echo === VolvoFlashWR Normal Mode ===
echo.

echo Setting up environment for normal mode...
echo.

REM Set environment variables for library loading
set PATH=%PATH%;%CD%\Libraries;%CD%\Drivers\Vocom
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=true

echo Environment configured for normal mode operation
echo.

REM Start the application from Application folder
cd /d "Application"
if exist "VolvoFlashWR.Launcher.exe" (
    echo Starting VolvoFlashWR (x64 with Bridge Service)...
    echo.
    "VolvoFlashWR.Launcher.exe"
) else (
    echo X VolvoFlashWR.Launcher.exe not found in Application folder!
    echo Please ensure the application is properly deployed.
    echo Expected location: Application\VolvoFlashWR.Launcher.exe
    echo.
    pause
    exit /b 1
)

echo.
echo Application closed.
pause
