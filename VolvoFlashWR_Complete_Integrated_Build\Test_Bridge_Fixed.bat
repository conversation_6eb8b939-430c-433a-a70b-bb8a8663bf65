@echo off
title Test Bridge Service - Fixed Version
echo === Testing Fixed Bridge Service ===
echo.

echo This test will run the application with the fixed bridge service
echo that doesn't require APCI libraries to initialize.
echo.

REM Set environment variables for testing
set SKIP_VCREDIST_DOWNLOAD=true
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=true

echo Environment variables set for testing:
echo - SKIP_VCREDIST_DOWNLOAD=true
echo - USE_PATCHED_IMPLEMENTATION=true
echo - VERBOSE_LOGGING=true
echo - PHOENIX_VOCOM_ENABLED=true
echo.

cd /d "Application"

echo Starting application with fixed bridge service...
echo Note: The bridge should now initialize successfully in simulation mode
echo.

"VolvoFlashWR.Launcher.exe"

echo.
echo Application test completed.
echo Check the logs in Application/Logs/ folder for bridge initialization status.
pause
