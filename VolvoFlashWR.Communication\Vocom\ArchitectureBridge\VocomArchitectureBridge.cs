using System;
using System.Diagnostics;
using System.IO;
using System.IO.Pipes;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom.ArchitectureBridge
{
    /// <summary>
    /// Simple data transfer object for bridge communication
    /// This matches the BridgeVocomDevice in the bridge process
    /// </summary>
    public class BridgeVocomDevice
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ConnectionType { get; set; } = string.Empty;
        public string ConnectionStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Architecture bridge that handles communication between x64 main process and x86 APCI libraries
    /// This solves the Error 193 architecture mismatch issue by using process isolation
    /// </summary>
    public class VocomArchitectureBridge : IDisposable
    {
        private readonly ILoggingService _logger;
        private Process? _bridgeProcess;
        private NamedPipeServerStream? _pipeServer;
        private NamedPipeClientStream? _pipeClient;
        private readonly string _pipeName;
        private bool _isInitialized;
        private bool _disposed;

        /// <summary>
        /// Gets whether the bridge is available and ready for use
        /// </summary>
        public bool IsAvailable => _isInitialized && _bridgeProcess != null && !_bridgeProcess.HasExited;

        public VocomArchitectureBridge(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            // Use process ID and timestamp to ensure unique pipe name
            _pipeName = $"VocomBridge_{Environment.ProcessId}_{DateTime.Now.Ticks}";
        }

        /// <summary>
        /// Initializes the architecture bridge by starting the x86 bridge process
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Vocom Architecture Bridge", "VocomArchitectureBridge");

                // Check if already initialized
                if (_isInitialized)
                {
                    _logger.LogInformation("Bridge already initialized", "VocomArchitectureBridge");
                    return true;
                }

                // Create named pipe server for communication
                _pipeServer = new NamedPipeServerStream(_pipeName, PipeDirection.InOut, 1, PipeTransmissionMode.Message);

                // Start the x86 bridge process
                if (!await StartBridgeProcessAsync())
                {
                    _logger.LogError("Failed to start bridge process", "VocomArchitectureBridge");
                    return false;
                }

                // Wait for bridge process to connect
                _logger.LogInformation("Waiting for bridge process to connect...", "VocomArchitectureBridge");
                await _pipeServer.WaitForConnectionAsync();
                _logger.LogInformation("Bridge process connected successfully", "VocomArchitectureBridge");

                // Send initialization command
                var initCommand = new BridgeCommand
                {
                    Type = "Initialize",
                    Data = JsonSerializer.Serialize(new { LibrariesPath = GetLibrariesPath() })
                };

                var response = await SendCommandAsync(initCommand);
                if (response?.Success == true)
                {
                    _isInitialized = true;
                    _logger.LogInformation("Architecture bridge initialized successfully", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Bridge initialization failed: {response?.Message}", "VocomArchitectureBridge");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during bridge initialization: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Detects Vocom devices through the architecture bridge
        /// </summary>
        public async Task<VocomDevice[]> DetectDevicesAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot detect devices", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }

            try
            {
                var command = new BridgeCommand { Type = "DetectDevices" };
                var response = await SendCommandAsync(command);

                if (response?.Success == true && !string.IsNullOrEmpty(response.Data))
                {
                    _logger.LogInformation($"Bridge response data: {response.Data}", "VocomArchitectureBridge");

                    try
                    {
                        // Try to deserialize as raw JSON first to see the structure
                        _logger.LogInformation($"Attempting to parse JSON response: {response.Data}", "VocomArchitectureBridge");

                        // Parse as JsonDocument first to analyze structure
                        using var jsonDocument = JsonDocument.Parse(response.Data);
                        _logger.LogInformation($"JSON root element kind: {jsonDocument.RootElement.ValueKind}", "VocomArchitectureBridge");

                        if (jsonDocument.RootElement.ValueKind == JsonValueKind.Array)
                        {
                            _logger.LogInformation($"JSON array length: {jsonDocument.RootElement.GetArrayLength()}", "VocomArchitectureBridge");
                            if (jsonDocument.RootElement.GetArrayLength() > 0)
                            {
                                var firstElement = jsonDocument.RootElement[0];
                                _logger.LogInformation($"First element properties:", "VocomArchitectureBridge");
                                foreach (var property in firstElement.EnumerateObject())
                                {
                                    _logger.LogInformation($"  {property.Name}: {property.Value} (Type: {property.Value.ValueKind})", "VocomArchitectureBridge");
                                }
                            }
                        }

                        // Now try to deserialize as BridgeVocomDevice array
                        _logger.LogInformation("Attempting to deserialize as BridgeVocomDevice[]", "VocomArchitectureBridge");
                        var bridgeDevices = JsonSerializer.Deserialize<BridgeVocomDevice[]>(response.Data);
                        if (bridgeDevices != null)
                        {
                            _logger.LogInformation($"Successfully deserialized {bridgeDevices.Length} bridge devices", "VocomArchitectureBridge");
                            var devices = ConvertBridgeDevicesToVocomDevices(bridgeDevices);
                            _logger.LogInformation($"Detected {devices.Length} Vocom devices through bridge", "VocomArchitectureBridge");
                            return devices;
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError($"JSON deserialization error: {ex.Message}", "VocomArchitectureBridge");
                        _logger.LogError($"Response data: {response.Data}", "VocomArchitectureBridge");
                        _logger.LogError($"Exception type: {ex.GetType().FullName}", "VocomArchitectureBridge");
                        _logger.LogError($"Stack trace: {ex.StackTrace}", "VocomArchitectureBridge");

                        // Return empty array instead of throwing to prevent application crash
                        _logger.LogWarning("Returning empty device array due to JSON deserialization error", "VocomArchitectureBridge");
                        return Array.Empty<VocomDevice>();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Unexpected error during JSON processing: {ex.Message}", "VocomArchitectureBridge");
                        _logger.LogError($"Exception type: {ex.GetType().FullName}", "VocomArchitectureBridge");
                        return Array.Empty<VocomDevice>();
                    }
                }

                _logger.LogWarning($"Device detection failed: {response?.Message}", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device detection: {ex.Message}", "VocomArchitectureBridge");
                return Array.Empty<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a Vocom device through the architecture bridge
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(string deviceId)
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Bridge not initialized, cannot connect to device", "VocomArchitectureBridge");
                return false;
            }

            try
            {
                var command = new BridgeCommand
                {
                    Type = "ConnectDevice",
                    Data = JsonSerializer.Serialize(new { DeviceId = deviceId })
                };

                var response = await SendCommandAsync(command);
                if (response?.Success == true)
                {
                    _logger.LogInformation($"Successfully connected to device {deviceId} through bridge", "VocomArchitectureBridge");
                    return true;
                }

                _logger.LogError($"Failed to connect to device {deviceId}: {response?.Message}", "VocomArchitectureBridge");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device connection: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Sends a command to the bridge process and waits for response
        /// </summary>
        private async Task<BridgeResponse?> SendCommandAsync(BridgeCommand command)
        {
            if (_pipeServer == null || !_pipeServer.IsConnected)
            {
                _logger.LogError("Pipe server not connected", "VocomArchitectureBridge");
                return null;
            }

            try
            {
                // Serialize and send command
                var commandJson = JsonSerializer.Serialize(command);
                var commandBytes = Encoding.UTF8.GetBytes(commandJson);

                await _pipeServer.WriteAsync(commandBytes, 0, commandBytes.Length);
                await _pipeServer.FlushAsync();

                // Read response
                var buffer = new byte[4096];
                var bytesRead = await _pipeServer.ReadAsync(buffer, 0, buffer.Length);
                var responseJson = Encoding.UTF8.GetString(buffer, 0, bytesRead);

                return JsonSerializer.Deserialize<BridgeResponse>(responseJson);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during command communication: {ex.Message}", "VocomArchitectureBridge");
                return null;
            }
        }

        /// <summary>
        /// Starts the x86 bridge process
        /// </summary>
        private async Task<bool> StartBridgeProcessAsync()
        {
            try
            {
                var bridgeExePath = GetBridgeExecutablePath();
                if (!File.Exists(bridgeExePath))
                {
                    _logger.LogError($"Bridge executable not found at {bridgeExePath}", "VocomArchitectureBridge");
                    return false;
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = bridgeExePath,
                    Arguments = _pipeName,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                _bridgeProcess = Process.Start(startInfo);
                if (_bridgeProcess == null)
                {
                    _logger.LogError("Failed to start bridge process", "VocomArchitectureBridge");
                    return false;
                }

                _logger.LogInformation($"Started bridge process with PID {_bridgeProcess.Id}", "VocomArchitectureBridge");

                // Give the process a moment to start
                await Task.Delay(1000);

                return !_bridgeProcess.HasExited;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception starting bridge process: {ex.Message}", "VocomArchitectureBridge");
                return false;
            }
        }

        /// <summary>
        /// Gets the path to the bridge executable
        /// </summary>
        private string GetBridgeExecutablePath()
        {
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appPath, "Bridge", "VolvoFlashWR.VocomBridge.exe");
        }

        /// <summary>
        /// Converts BridgeVocomDevice array to VocomDevice array
        /// </summary>
        private VocomDevice[] ConvertBridgeDevicesToVocomDevices(BridgeVocomDevice[] bridgeDevices)
        {
            var devices = new VocomDevice[bridgeDevices.Length];
            for (int i = 0; i < bridgeDevices.Length; i++)
            {
                var bridgeDevice = bridgeDevices[i];
                devices[i] = new VocomDevice
                {
                    Id = bridgeDevice.Id,
                    Name = bridgeDevice.Name,
                    ConnectionType = ParseConnectionType(bridgeDevice.ConnectionType),
                    ConnectionStatus = ParseConnectionStatus(bridgeDevice.ConnectionStatus)
                };
            }
            return devices;
        }

        /// <summary>
        /// Parses connection type string to enum
        /// </summary>
        private VocomConnectionType ParseConnectionType(string connectionType)
        {
            return connectionType.ToUpperInvariant() switch
            {
                "USB" => VocomConnectionType.USB,
                "BLUETOOTH" => VocomConnectionType.Bluetooth,
                "WIFI" => VocomConnectionType.WiFi,
                _ => VocomConnectionType.USB
            };
        }

        /// <summary>
        /// Parses connection status string to enum
        /// </summary>
        private VocomConnectionStatus ParseConnectionStatus(string connectionStatus)
        {
            return connectionStatus.ToUpperInvariant() switch
            {
                "CONNECTED" => VocomConnectionStatus.Connected,
                "DISCONNECTED" => VocomConnectionStatus.Disconnected,
                "CONNECTING" => VocomConnectionStatus.Connecting,
                "ERROR" => VocomConnectionStatus.Error,
                _ => VocomConnectionStatus.Disconnected
            };
        }

        /// <summary>
        /// Gets the libraries path for the bridge process
        /// </summary>
        private string GetLibrariesPath()
        {
            var appPath = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appPath, "Libraries");
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _pipeServer?.Dispose();
                _pipeClient?.Dispose();

                if (_bridgeProcess != null && !_bridgeProcess.HasExited)
                {
                    _bridgeProcess.Kill();
                    _bridgeProcess.Dispose();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disposal: {ex.Message}", "VocomArchitectureBridge");
            }

            _disposed = true;
        }
    }

    /// <summary>
    /// Command sent to the bridge process
    /// </summary>
    public class BridgeCommand
    {
        public string Type { get; set; } = string.Empty;
        public string? Data { get; set; }
    }

    /// <summary>
    /// Response from the bridge process
    /// </summary>
    public class BridgeResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? Data { get; set; }
    }
}
