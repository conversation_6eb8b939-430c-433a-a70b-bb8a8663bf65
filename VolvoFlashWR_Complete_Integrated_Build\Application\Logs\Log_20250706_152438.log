Log started at 7/6/2025 3:24:38 PM
2025-07-06 15:24:38.870 [Information] LoggingService: Logging service initialized
2025-07-06 15:24:38.887 [Information] App: Starting integrated application initialization
2025-07-06 15:24:38.889 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 15:24:38.892 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 15:24:38.896 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 15:24:38.897 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 15:24:38.899 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 15:24:38.901 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 15:24:38.904 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 15:24:38.914 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:24:38.916 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.919 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.920 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 15:24:38.923 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:24:38.927 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.930 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.931 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:24:38.934 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:24:38.936 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.944 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.945 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:24:38.954 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:24:38.957 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.963 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:24:38.964 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:24:38.967 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 15:24:38.971 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 15:24:38.971 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 15:24:38.974 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 15:24:38.974 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 15:24:38.977 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 15:24:38.977 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 15:24:38.978 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 15:24:38.979 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 15:24:38.980 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 15:24:38.980 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 15:24:38.981 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:24:38.981 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:24:38.982 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:24:38.988 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 15:24:38.989 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 15:24:38.989 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 15:24:38.997 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 15:24:38.997 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 15:24:39.000 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 15:24:39.002 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 15:24:39.006 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 15:24:39.008 [Information] LibraryExtractor: Copying system libraries
2025-07-06 15:24:39.016 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 15:24:39.024 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 15:25:10.410 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 15:26:10.696 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 15:27:07.137 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 15:27:57.613 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:28:42.696 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:29:30.261 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:30:18.920 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 15:30:18.920 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 15:30:18.921 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 15:30:18.923 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 15:30:18.924 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 15:30:18.924 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 15:30:18.931 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 15:30:18.934 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 15:30:18.936 [Information] DependencyManager: Initializing dependency manager
2025-07-06 15:30:18.938 [Information] DependencyManager: Setting up library search paths
2025-07-06 15:30:18.939 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:30:18.940 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 15:30:18.941 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 15:30:18.941 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 15:30:18.945 [Information] DependencyManager: Verifying required directories
2025-07-06 15:30:18.945 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:30:18.946 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 15:30:18.947 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 15:30:18.948 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 15:30:18.950 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 15:30:18.958 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 15:30:18.960 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 15:30:18.964 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 15:30:18.967 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 15:30:18.971 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 15:30:18.973 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:30:18.974 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:30:18.975 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:30:18.980 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 15:30:18.982 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 15:30:18.983 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 15:30:18.983 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 15:30:18.984 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 15:30:18.984 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 15:30:18.986 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 15:30:18.986 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 15:30:18.987 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 15:30:18.987 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 15:30:18.988 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 15:30:18.989 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 15:30:18.989 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 15:30:18.990 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 15:30:18.991 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 15:30:18.991 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 15:30:18.992 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 15:30:18.993 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 15:30:18.993 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 15:30:18.994 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 15:30:18.995 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 15:30:18.996 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 15:30:18.997 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 15:30:18.998 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 15:30:18.999 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 15:30:19.000 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 15:30:19.001 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 15:30:19.003 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 15:30:19.004 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 15:30:19.005 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 15:30:19.006 [Information] DependencyManager: Setting up environment variables
2025-07-06 15:30:19.007 [Information] DependencyManager: Environment variables configured
2025-07-06 15:30:19.009 [Information] DependencyManager: Verifying library loading status
2025-07-06 15:30:19.443 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 15:30:19.446 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 15:30:19.446 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 15:30:19.450 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 15:30:19.452 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 15:30:19.460 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 15:30:19.464 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 15:30:19.465 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 15:30:19.465 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 15:30:19.468 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 15:30:19.468 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 15:30:19.469 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:30:19.469 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 15:30:19.469 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 15:30:19.470 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 15:30:19.470 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 15:30:19.471 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:30:19.471 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 15:30:19.472 [Information] App: Integrated startup completed successfully
2025-07-06 15:30:19.475 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 15:30:19.492 [Information] App: Initializing application services
2025-07-06 15:30:19.496 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 15:30:19.497 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 15:30:19.558 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 15:30:19.559 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 15:30:19.561 [Information] App: Configuration service initialized successfully
2025-07-06 15:30:19.564 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 15:30:19.564 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 15:30:19.575 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 15:30:19.576 [Information] App: Final useDummyImplementations value: False
2025-07-06 15:30:19.577 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 15:30:19.581 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 15:30:19.606 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 15:30:19.608 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 15:30:19.608 [Information] App: usePatchedImplementation flag is: True
2025-07-06 15:30:19.609 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 15:30:19.609 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 15:30:19.609 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 15:30:19.610 [Information] App: verboseLogging flag is: True
2025-07-06 15:30:19.614 [Information] App: Verifying real hardware requirements...
2025-07-06 15:30:19.615 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 15:30:19.615 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 15:30:19.616 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 15:30:19.616 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 15:30:19.617 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 15:30:19.617 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 15:30:19.618 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 15:30:19.618 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 15:30:19.630 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 15:30:19.633 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 15:30:19.635 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 15:30:19.638 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 15:30:19.639 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 15:30:19.640 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 15:30:19.640 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 15:30:19.640 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 15:30:19.641 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 15:30:19.641 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 15:30:19.642 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 15:30:19.642 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 15:30:19.644 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 15:30:19.646 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 15:30:19.649 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 15:30:19.652 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 15:30:19.652 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 15:30:19.653 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 15:30:19.655 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 15:30:19.655 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 15:30:19.657 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 15:30:19.659 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 15:30:19.665 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 15:30:19.675 [Information] VocomArchitectureBridge: Started bridge process with PID 16868
2025-07-06 15:30:20.680 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 15:30:20.685 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 15:30:20.836 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 15:30:20.838 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 15:30:20.839 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 15:30:20.839 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 15:30:20.840 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 15:30:20.840 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 15:30:20.841 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 15:30:20.841 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 15:30:20.841 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 15:30:20.842 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 15:30:20.842 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 15:30:20.915 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:30:20.986 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:30:20.988 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:30:20.988 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 15:30:20.993 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 15:30:21.000 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:21.001 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 15:30:21.006 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 15:30:21.008 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 15:30:21.009 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 15:30:21.015 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 15:30:21.017 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 15:30:21.023 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 15:30:21.028 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 15:30:21.033 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 15:30:21.044 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:30:21.051 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:30:21.052 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:21.057 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:30:21.058 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:30:21.058 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:21.063 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:30:21.064 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:30:21.064 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:21.067 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:30:21.068 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:30:21.069 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:21.073 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:30:21.074 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:30:21.075 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:21.076 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 15:30:21.083 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 15:30:21.085 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:21.085 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 15:30:21.086 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 15:30:21.087 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:21.087 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 15:30:21.088 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 15:30:21.088 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:21.089 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 15:30:21.089 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 15:30:21.090 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:21.091 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 15:30:21.091 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 15:30:21.091 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 15:30:21.093 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:30:21.099 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:30:21.099 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:30:21.101 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 15:30:21.101 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 15:30:21.103 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 15:30:22.104 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 15:30:22.104 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 15:30:22.105 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 15:30:22.106 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 15:30:22.106 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 15:30:22.106 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 15:30:22.109 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 15:30:22.110 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 15:30:22.111 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 15:30:22.111 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:30:22.113 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:30:22.114 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:22.114 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:30:22.114 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:30:22.115 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:22.115 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:30:22.116 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:30:22.116 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:22.116 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:30:22.117 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:30:22.118 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:22.118 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:30:22.118 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:30:22.119 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:22.119 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 15:30:22.119 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 15:30:22.120 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:22.120 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 15:30:22.120 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 15:30:22.121 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:22.121 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 15:30:22.122 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 15:30:22.122 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:22.122 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 15:30:22.123 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 15:30:22.123 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:22.123 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 15:30:22.124 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 15:30:22.124 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 15:30:22.124 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:30:22.126 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:30:22.126 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:30:22.127 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 15:30:22.127 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 15:30:22.127 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 15:30:24.129 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 15:30:24.130 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 15:30:24.130 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 15:30:24.130 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 15:30:24.131 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 15:30:24.131 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 15:30:24.132 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 15:30:24.133 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 15:30:24.134 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 15:30:24.134 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:30:24.135 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:30:24.135 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:24.135 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:30:24.136 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:30:24.136 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:24.136 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:30:24.136 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:30:24.137 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:24.137 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:30:24.137 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:30:24.138 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:24.138 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:30:24.138 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:30:24.139 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:30:24.139 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 15:30:24.139 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 15:30:24.140 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:24.140 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 15:30:24.141 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 15:30:24.141 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:24.141 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 15:30:24.142 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 15:30:24.142 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:24.142 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 15:30:24.142 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 15:30:24.143 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:30:24.143 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 15:30:24.143 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 15:30:24.144 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 15:30:24.144 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:30:24.145 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:30:24.146 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:30:24.146 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 15:30:24.147 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 15:30:24.147 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 15:30:27.148 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 15:30:27.148 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 15:30:27.149 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 15:30:27.151 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 15:30:27.652 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 15:30:27.653 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 15:30:27.654 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 15:30:27.655 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 15:30:27.659 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 15:30:27.661 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 15:30:27.667 [Information] BackupService: Initializing backup service
2025-07-06 15:30:27.667 [Information] BackupService: Backup service initialized successfully
2025-07-06 15:30:27.667 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 15:30:27.668 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 15:30:27.671 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 15:30:27.702 [Information] BackupService: Compressing backup data
2025-07-06 15:30:27.716 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 15:30:27.717 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 15:30:27.718 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 15:30:27.719 [Information] BackupService: Compressing backup data
2025-07-06 15:30:27.720 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-06 15:30:27.721 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 15:30:27.721 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 15:30:27.722 [Information] BackupService: Compressing backup data
2025-07-06 15:30:27.723 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-06 15:30:27.723 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 15:30:27.723 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 15:30:27.724 [Information] BackupService: Compressing backup data
2025-07-06 15:30:27.725 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (448 bytes)
2025-07-06 15:30:27.725 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 15:30:27.725 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 15:30:27.726 [Information] BackupService: Compressing backup data
2025-07-06 15:30:27.736 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-06 15:30:27.736 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 15:30:27.737 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 15:30:27.737 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 15:30:27.739 [Information] BackupService: Compressing backup data
2025-07-06 15:30:27.740 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-06 15:30:27.741 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 15:30:27.741 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 15:30:27.745 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 15:30:27.758 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 15:30:27.761 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 15:30:27.842 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 15:30:27.843 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 15:30:27.844 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 15:30:27.845 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 15:30:27.845 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 15:30:27.847 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 15:30:27.848 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 15:30:27.852 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 15:30:27.853 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 15:30:27.863 [Information] LicensingService: Initializing licensing service
2025-07-06 15:30:27.920 [Information] LicensingService: License information loaded successfully
2025-07-06 15:30:27.923 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 15:30:27.923 [Information] App: Licensing service initialized successfully
2025-07-06 15:30:27.924 [Information] App: License status: Trial
2025-07-06 15:30:27.924 [Information] App: Trial period: 29 days remaining
2025-07-06 15:30:27.925 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 15:30:28.116 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 15:30:28.116 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 15:30:28.117 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 15:30:28.117 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 15:30:28.168 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 15:30:28.669 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 15:30:28.720 [Information] BackupService: Initializing backup service
2025-07-06 15:30:28.721 [Information] BackupService: Backup service initialized successfully
2025-07-06 15:30:28.772 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 15:30:28.773 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 15:30:28.775 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 15:30:28.775 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 15:30:28.828 [Information] BackupService: Getting predefined backup categories
2025-07-06 15:30:28.880 [Information] MainViewModel: Services initialized successfully
2025-07-06 15:30:28.893 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 15:30:28.895 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:30:28.908 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:30:28.909 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:30:28.910 [Information] MainViewModel: Found 0 Vocom device(s)
