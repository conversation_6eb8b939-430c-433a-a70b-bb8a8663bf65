Log started at 7/6/2025 4:20:42 PM
2025-07-06 16:20:42.546 [Information] LoggingService: Logging service initialized
2025-07-06 16:20:42.594 [Information] App: Starting integrated application initialization
2025-07-06 16:20:42.596 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 16:20:42.601 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 16:20:42.604 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 16:20:42.606 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 16:20:42.609 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 16:20:42.612 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 16:20:42.616 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 16:20:42.627 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:20:42.630 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.634 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.635 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 16:20:42.640 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:20:42.644 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.647 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.648 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:20:42.652 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:20:42.656 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.661 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.662 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:20:42.667 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 16:20:42.671 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.674 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 16:20:42.675 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:20:42.678 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 16:20:42.681 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 16:20:42.681 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 16:20:42.683 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 16:20:42.684 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 16:20:42.685 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 16:20:42.686 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 16:20:42.687 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 16:20:42.688 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 16:20:42.688 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 16:20:42.689 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 16:20:42.689 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:20:42.690 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:20:42.690 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:20:42.699 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 16:20:42.700 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 16:20:42.701 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 16:20:42.709 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 16:20:42.709 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 16:20:42.712 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 16:20:42.714 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 16:20:42.719 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 16:20:42.722 [Information] LibraryExtractor: Copying system libraries
2025-07-06 16:20:42.728 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 16:20:42.738 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 16:21:10.890 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 16:22:21.258 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 16:23:23.465 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 16:24:12.158 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:25:07.079 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:25:55.625 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:26:45.656 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 16:26:45.657 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 16:26:45.658 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 16:26:45.658 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 16:26:45.659 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 16:26:45.660 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 16:26:45.667 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 16:26:45.671 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 16:26:45.673 [Information] DependencyManager: Initializing dependency manager
2025-07-06 16:26:45.675 [Information] DependencyManager: Setting up library search paths
2025-07-06 16:26:45.677 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:26:45.679 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:26:45.680 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 16:26:45.681 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 16:26:45.683 [Information] DependencyManager: Verifying required directories
2025-07-06 16:26:45.684 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:26:45.684 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:26:45.685 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 16:26:45.685 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 16:26:45.689 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 16:26:45.699 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 16:26:45.703 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 16:26:45.707 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 16:26:45.714 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 16:26:45.715 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 16:26:45.716 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:26:45.717 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:26:45.718 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:26:45.720 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 16:26:45.722 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 16:26:45.723 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 16:26:45.724 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 16:26:45.724 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 16:26:45.725 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 16:26:45.726 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 16:26:45.727 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 16:26:45.728 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 16:26:45.729 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 16:26:45.729 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 16:26:45.731 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 16:26:45.731 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 16:26:45.732 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 16:26:45.733 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 16:26:45.733 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 16:26:45.734 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 16:26:45.735 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 16:26:45.736 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 16:26:45.736 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 16:26:45.737 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 16:26:45.738 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 16:26:45.739 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 16:26:45.739 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 16:26:45.740 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 16:26:45.742 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 16:26:45.743 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 16:26:45.746 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 16:26:45.747 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 16:26:45.748 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 16:26:45.749 [Information] DependencyManager: Setting up environment variables
2025-07-06 16:26:45.750 [Information] DependencyManager: Environment variables configured
2025-07-06 16:26:45.752 [Information] DependencyManager: Verifying library loading status
2025-07-06 16:26:46.281 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 16:26:46.282 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 16:26:46.282 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 16:26:46.286 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 16:26:46.287 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 16:26:46.292 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 16:26:46.297 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 16:26:46.298 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 16:26:46.298 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 16:26:46.301 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 16:26:46.301 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 16:26:46.302 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:26:46.302 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:26:46.302 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 16:26:46.303 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 16:26:46.303 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 16:26:46.304 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:26:46.304 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 16:26:46.305 [Information] App: Integrated startup completed successfully
2025-07-06 16:26:46.310 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 16:26:46.337 [Information] App: Initializing application services
2025-07-06 16:26:46.340 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 16:26:46.341 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 16:26:46.456 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 16:26:46.457 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 16:26:46.458 [Information] App: Configuration service initialized successfully
2025-07-06 16:26:46.462 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 16:26:46.463 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 16:26:46.470 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 16:26:46.471 [Information] App: Final useDummyImplementations value: False
2025-07-06 16:26:46.471 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 16:26:46.473 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 16:26:46.494 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 16:26:46.495 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 16:26:46.496 [Information] App: usePatchedImplementation flag is: True
2025-07-06 16:26:46.497 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 16:26:46.497 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 16:26:46.497 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 16:26:46.498 [Information] App: verboseLogging flag is: True
2025-07-06 16:26:46.500 [Information] App: Verifying real hardware requirements...
2025-07-06 16:26:46.501 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 16:26:46.501 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 16:26:46.502 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 16:26:46.502 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 16:26:46.503 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 16:26:46.503 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 16:26:46.504 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 16:26:46.504 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 16:26:46.516 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 16:26:46.519 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 16:26:46.523 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 16:26:46.526 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 16:26:46.526 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 16:26:46.527 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 16:26:46.528 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 16:26:46.529 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 16:26:46.529 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 16:26:46.530 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 16:26:46.530 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 16:26:46.531 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 16:26:46.532 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 16:26:46.532 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 16:26:46.535 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 16:26:46.535 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 16:26:46.536 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 16:26:46.536 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 16:26:46.539 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 16:26:46.539 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 16:26:46.540 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 16:26:46.543 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:26:46.549 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:26:46.559 [Information] VocomArchitectureBridge: Started bridge process with PID 10888
2025-07-06 16:26:47.563 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 16:26:47.568 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 16:26:47.712 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 16:26:47.714 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:26:47.714 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 16:26:47.715 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 16:26:47.715 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:26:47.716 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:26:47.716 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 16:26:47.717 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:26:47.717 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 16:26:47.717 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 16:26:47.718 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 16:26:47.815 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:26:47.915 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:26:47.916 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:26:47.917 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 16:26:47.922 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 16:26:47.927 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:47.930 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 16:26:47.936 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:26:47.940 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:26:47.940 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:26:47.947 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:26:47.951 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:26:47.963 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:26:47.967 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:26:47.975 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:26:47.988 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:26:47.993 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:26:47.997 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:48.002 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:26:48.002 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:26:48.003 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:48.006 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:26:48.007 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:26:48.007 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:48.010 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:26:48.011 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:26:48.012 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:48.017 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:26:48.018 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:26:48.018 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:48.020 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:26:48.025 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:26:48.028 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:48.028 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:26:48.029 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:26:48.030 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:48.030 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:26:48.031 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:26:48.031 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:48.032 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:26:48.032 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:26:48.033 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:48.033 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:26:48.034 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:26:48.034 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:26:48.036 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:26:48.048 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:26:48.048 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:26:48.049 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:26:48.050 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:26:48.051 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 16:26:49.052 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 16:26:49.053 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:26:49.053 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:26:49.054 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:26:49.054 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:26:49.054 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:26:49.056 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:26:49.056 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:26:49.057 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:26:49.058 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:26:49.058 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:26:49.058 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:49.059 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:26:49.059 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:26:49.059 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:49.060 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:26:49.060 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:26:49.061 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:49.061 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:26:49.062 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:26:49.063 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:49.063 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:26:49.064 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:26:49.064 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:49.065 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:26:49.065 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:26:49.065 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:49.066 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:26:49.066 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:26:49.066 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:49.067 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:26:49.067 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:26:49.068 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:49.068 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:26:49.069 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:26:49.069 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:49.071 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:26:49.071 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:26:49.072 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:26:49.072 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:26:49.076 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:26:49.076 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:26:49.076 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:26:49.077 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:26:49.077 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 16:26:51.079 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 16:26:51.119 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:26:51.119 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:26:51.119 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:26:51.120 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:26:51.120 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:26:51.121 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:26:51.122 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:26:51.122 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:26:51.123 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:26:51.123 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:26:51.124 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:51.124 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:26:51.125 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:26:51.125 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:51.125 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:26:51.125 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:26:51.126 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:51.126 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:26:51.126 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:26:51.127 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:51.127 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:26:51.127 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:26:51.128 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:26:51.128 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:26:51.128 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:26:51.130 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:51.130 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:26:51.131 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:26:51.131 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:51.131 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:26:51.132 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:26:51.132 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:51.132 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:26:51.133 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:26:51.133 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:26:51.133 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:26:51.134 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:26:51.134 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:26:51.134 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:26:51.136 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:26:51.136 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:26:51.137 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:26:51.137 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:26:51.137 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 16:26:54.138 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 16:26:54.139 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 16:26:54.141 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 16:26:54.143 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 16:26:54.644 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 16:26:54.644 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 16:26:54.645 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 16:26:54.646 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 16:26:54.651 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 16:26:54.653 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 16:26:54.656 [Information] BackupService: Initializing backup service
2025-07-06 16:26:54.657 [Information] BackupService: Backup service initialized successfully
2025-07-06 16:26:54.657 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 16:26:54.658 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 16:26:54.660 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 16:26:54.690 [Information] BackupService: Compressing backup data
2025-07-06 16:26:54.703 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-06 16:26:54.705 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 16:26:54.705 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 16:26:54.706 [Information] BackupService: Compressing backup data
2025-07-06 16:26:54.708 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-06 16:26:54.709 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 16:26:54.709 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 16:26:54.710 [Information] BackupService: Compressing backup data
2025-07-06 16:26:54.711 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-06 16:26:54.712 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 16:26:54.712 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 16:26:54.713 [Information] BackupService: Compressing backup data
2025-07-06 16:26:54.715 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-06 16:26:54.715 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 16:26:54.716 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 16:26:54.716 [Information] BackupService: Compressing backup data
2025-07-06 16:26:54.718 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-06 16:26:54.718 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 16:26:54.732 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 16:26:54.733 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 16:26:54.734 [Information] BackupService: Compressing backup data
2025-07-06 16:26:54.735 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-06 16:26:54.736 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 16:26:54.736 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 16:26:54.738 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 16:26:54.743 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 16:26:54.745 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 16:26:54.815 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 16:26:54.816 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 16:26:54.817 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 16:26:54.817 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 16:26:54.818 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 16:26:54.819 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 16:26:54.820 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 16:26:54.824 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 16:26:54.824 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 16:26:54.836 [Information] LicensingService: Initializing licensing service
2025-07-06 16:26:54.901 [Information] LicensingService: License information loaded successfully
2025-07-06 16:26:54.904 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 16:26:54.905 [Information] App: Licensing service initialized successfully
2025-07-06 16:26:54.905 [Information] App: License status: Trial
2025-07-06 16:26:54.906 [Information] App: Trial period: 29 days remaining
2025-07-06 16:26:54.907 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 16:26:55.095 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:26:55.095 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:26:55.095 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 16:26:55.096 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:26:55.149 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 16:26:55.649 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 16:26:55.700 [Information] BackupService: Initializing backup service
2025-07-06 16:26:55.700 [Information] BackupService: Backup service initialized successfully
2025-07-06 16:26:55.752 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 16:26:55.753 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 16:26:55.755 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 16:26:55.756 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 16:26:55.808 [Information] BackupService: Getting predefined backup categories
2025-07-06 16:26:55.860 [Information] MainViewModel: Services initialized successfully
2025-07-06 16:26:55.863 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:26:55.865 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:26:55.869 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:26:55.869 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:26:55.870 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 16:27:44.624 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:27:44.625 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:27:44.633 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:27:44.633 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:27:44.634 [Information] MainViewModel: Found 0 Vocom device(s)
