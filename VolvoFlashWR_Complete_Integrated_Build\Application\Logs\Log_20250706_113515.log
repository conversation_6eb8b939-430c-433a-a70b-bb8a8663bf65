Log started at 7/6/2025 11:35:15 AM
2025-07-06 11:35:15.840 [Information] LoggingService: Logging service initialized
2025-07-06 11:35:15.860 [Information] App: Starting integrated application initialization
2025-07-06 11:35:15.862 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 11:35:15.865 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 11:35:15.867 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 11:35:15.868 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 11:35:15.870 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 11:35:15.872 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 11:35:15.876 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 11:35:15.884 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:35:15.887 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.889 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.890 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 11:35:15.894 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:35:15.897 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.899 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.900 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:35:15.903 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:35:15.905 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.909 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.910 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:35:15.913 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 11:35:15.916 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.918 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 11:35:15.919 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:35:15.921 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 11:35:15.924 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 11:35:15.924 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 11:35:15.926 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 11:35:15.927 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 11:35:15.927 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 11:35:15.928 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 11:35:15.928 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 11:35:15.929 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 11:35:15.929 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 11:35:15.930 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 11:35:15.930 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:35:15.931 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:35:15.931 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:35:15.938 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 11:35:15.939 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 11:35:15.940 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 11:35:15.947 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 11:35:15.947 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 11:35:15.949 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 11:35:15.951 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 11:35:15.954 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 11:35:15.956 [Information] LibraryExtractor: Copying system libraries
2025-07-06 11:35:15.962 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 11:35:15.971 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 11:35:38.695 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 11:36:21.712 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 11:37:03.703 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 11:37:46.167 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:38:50.397 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:39:34.463 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:40:17.193 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 11:40:17.194 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 11:40:17.194 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 11:40:17.194 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 11:40:17.195 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 11:40:17.195 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 11:40:17.201 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 11:40:17.203 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 11:40:17.204 [Information] DependencyManager: Initializing dependency manager
2025-07-06 11:40:17.205 [Information] DependencyManager: Setting up library search paths
2025-07-06 11:40:17.207 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:40:17.207 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 11:40:17.207 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 11:40:17.208 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 11:40:17.209 [Information] DependencyManager: Verifying required directories
2025-07-06 11:40:17.210 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:40:17.210 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 11:40:17.210 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 11:40:17.211 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 11:40:17.212 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 11:40:17.221 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 11:40:17.223 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 11:40:17.226 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 11:40:17.229 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 11:40:17.230 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 11:40:17.233 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:40:17.234 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:40:17.235 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:40:17.237 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 11:40:17.238 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 11:40:17.239 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 11:40:17.239 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 11:40:17.240 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 11:40:17.240 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 11:40:17.242 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 11:40:17.242 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 11:40:17.243 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 11:40:17.243 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 11:40:17.244 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 11:40:17.244 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 11:40:17.245 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 11:40:17.245 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 11:40:17.246 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 11:40:17.246 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 11:40:17.247 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 11:40:17.248 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 11:40:17.249 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 11:40:17.250 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 11:40:17.250 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 11:40:17.251 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 11:40:17.252 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 11:40:17.253 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 11:40:17.254 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 11:40:17.255 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 11:40:17.256 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 11:40:17.257 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 11:40:17.257 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 11:40:17.258 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 11:40:17.259 [Information] DependencyManager: Setting up environment variables
2025-07-06 11:40:17.259 [Information] DependencyManager: Environment variables configured
2025-07-06 11:40:17.261 [Information] DependencyManager: Verifying library loading status
2025-07-06 11:40:17.667 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 11:40:17.668 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 11:40:17.668 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 11:40:17.671 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 11:40:17.673 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 11:40:17.678 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 11:40:17.680 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 11:40:17.681 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 11:40:17.681 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 11:40:17.684 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 11:40:17.684 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 11:40:17.685 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:40:17.685 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 11:40:17.685 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 11:40:17.685 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 11:40:17.686 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 11:40:17.686 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 11:40:17.686 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 11:40:17.687 [Information] App: Integrated startup completed successfully
2025-07-06 11:40:17.690 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 11:40:17.712 [Information] App: Initializing application services
2025-07-06 11:40:17.718 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 11:40:17.719 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 11:40:17.784 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 11:40:17.787 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 11:40:17.790 [Information] App: Configuration service initialized successfully
2025-07-06 11:40:17.792 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 11:40:17.793 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 11:40:17.816 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 11:40:17.818 [Information] App: Final useDummyImplementations value: False
2025-07-06 11:40:17.819 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 11:40:17.822 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 11:40:17.854 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 11:40:17.858 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 11:40:17.859 [Information] App: usePatchedImplementation flag is: True
2025-07-06 11:40:17.861 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 11:40:17.861 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 11:40:17.862 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 11:40:17.862 [Information] App: verboseLogging flag is: True
2025-07-06 11:40:17.865 [Information] App: Verifying real hardware requirements...
2025-07-06 11:40:17.865 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 11:40:17.866 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 11:40:17.866 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 11:40:17.867 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 11:40:17.867 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 11:40:17.868 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 11:40:17.868 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 11:40:17.869 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 11:40:17.880 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 11:40:17.884 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 11:40:17.887 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 11:40:17.889 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 11:40:17.890 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 11:40:17.890 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 11:40:17.890 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 11:40:17.891 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 11:40:17.892 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 11:40:17.893 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 11:40:17.893 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 11:40:17.893 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 11:40:17.895 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 11:40:17.895 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 11:40:17.900 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 11:40:17.900 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 11:40:17.901 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 11:40:17.901 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 11:40:17.903 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 11:40:17.904 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 11:40:17.905 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 11:40:17.908 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 11:40:17.911 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 11:40:18.000 [Information] VocomArchitectureBridge: Started bridge process with PID 3504
2025-07-06 11:40:19.002 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 11:40:19.006 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 11:40:19.164 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 11:40:19.165 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 11:40:19.167 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 11:40:19.167 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 11:40:19.168 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 11:40:19.168 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 11:40:19.168 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 11:40:19.169 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 11:40:19.169 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 11:40:19.169 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 11:40:19.170 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 11:40:19.221 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:40:19.238 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:40:19.240 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:40:19.240 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 11:40:19.244 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 11:40:19.248 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:19.249 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 11:40:19.254 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 11:40:19.256 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 11:40:19.256 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 11:40:19.260 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 11:40:19.262 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 11:40:19.268 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 11:40:19.271 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 11:40:19.274 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 11:40:19.287 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:40:19.291 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:40:19.292 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:19.296 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:40:19.297 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:40:19.298 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:19.301 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:40:19.302 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:40:19.303 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:19.305 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:40:19.306 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:40:19.306 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:19.308 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:40:19.309 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:40:19.309 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:19.309 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 11:40:19.313 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 11:40:19.316 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:19.317 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 11:40:19.317 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 11:40:19.318 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:19.318 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 11:40:19.319 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 11:40:19.319 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:19.319 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 11:40:19.320 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 11:40:19.320 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:19.321 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 11:40:19.321 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 11:40:19.321 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 11:40:19.323 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:40:19.326 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:40:19.327 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:40:19.328 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 11:40:19.328 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 11:40:19.329 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 11:40:20.331 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 11:40:20.338 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 11:40:20.341 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 11:40:20.342 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 11:40:20.343 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 11:40:20.344 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 11:40:20.355 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 11:40:20.357 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 11:40:20.360 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 11:40:20.361 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:40:20.362 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:40:20.363 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:20.365 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:40:20.370 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:40:20.373 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:20.374 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:40:20.375 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:40:20.376 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:20.377 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:40:20.378 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:40:20.379 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:20.380 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:40:20.380 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:40:20.381 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:20.382 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 11:40:20.383 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 11:40:20.384 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:20.384 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 11:40:20.385 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 11:40:20.386 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:20.387 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 11:40:20.387 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 11:40:20.388 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:20.388 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 11:40:20.389 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 11:40:20.390 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:20.390 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 11:40:20.391 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 11:40:20.391 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 11:40:20.392 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:40:20.394 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:40:20.395 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:40:20.395 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 11:40:20.396 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 11:40:20.396 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 11:40:22.397 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 11:40:22.399 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 11:40:22.401 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 11:40:22.403 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 11:40:22.405 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 11:40:22.406 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 11:40:22.409 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 11:40:22.411 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 11:40:22.423 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 11:40:22.424 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:40:22.428 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 11:40:22.429 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:22.430 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:40:22.431 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 11:40:22.442 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:22.443 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:40:22.445 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 11:40:22.448 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:22.449 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:40:22.450 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 11:40:22.455 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:22.456 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:40:22.456 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 11:40:22.457 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 11:40:22.458 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 11:40:22.459 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 11:40:22.460 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:22.461 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 11:40:22.461 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 11:40:22.462 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:22.463 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 11:40:22.464 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 11:40:22.469 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:22.470 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 11:40:22.470 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 11:40:22.471 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 11:40:22.472 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 11:40:22.472 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 11:40:22.473 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 11:40:22.474 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:40:22.477 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:40:22.478 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:40:22.478 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 11:40:22.479 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 11:40:22.480 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 11:40:25.481 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 11:40:25.488 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 11:40:25.495 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 11:40:25.500 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 11:40:26.007 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 11:40:26.008 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 11:40:26.011 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 11:40:26.012 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 11:40:26.026 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 11:40:26.031 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 11:40:26.043 [Information] BackupService: Initializing backup service
2025-07-06 11:40:26.044 [Information] BackupService: Backup service initialized successfully
2025-07-06 11:40:26.045 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 11:40:26.046 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 11:40:26.052 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 11:40:26.122 [Information] BackupService: Compressing backup data
2025-07-06 11:40:26.132 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 11:40:26.134 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 11:40:26.134 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 11:40:26.136 [Information] BackupService: Compressing backup data
2025-07-06 11:40:26.138 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-06 11:40:26.139 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 11:40:26.139 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 11:40:26.140 [Information] BackupService: Compressing backup data
2025-07-06 11:40:26.142 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (443 bytes)
2025-07-06 11:40:26.142 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 11:40:26.143 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 11:40:26.143 [Information] BackupService: Compressing backup data
2025-07-06 11:40:26.144 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-07-06 11:40:26.145 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 11:40:26.146 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 11:40:26.146 [Information] BackupService: Compressing backup data
2025-07-06 11:40:26.148 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-06 11:40:26.148 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 11:40:26.148 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 11:40:26.149 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 11:40:26.149 [Information] BackupService: Compressing backup data
2025-07-06 11:40:26.154 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-06 11:40:26.154 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 11:40:26.154 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 11:40:26.156 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 11:40:26.160 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 11:40:26.162 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 11:40:26.236 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 11:40:26.237 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 11:40:26.239 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 11:40:26.239 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 11:40:26.240 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 11:40:26.241 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 11:40:26.242 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 11:40:26.245 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 11:40:26.246 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 11:40:26.258 [Information] LicensingService: Initializing licensing service
2025-07-06 11:40:26.311 [Information] LicensingService: License information loaded successfully
2025-07-06 11:40:26.314 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 11:40:26.315 [Information] App: Licensing service initialized successfully
2025-07-06 11:40:26.315 [Information] App: License status: Trial
2025-07-06 11:40:26.316 [Information] App: Trial period: 29 days remaining
2025-07-06 11:40:26.316 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 11:40:26.499 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 11:40:26.499 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 11:40:26.500 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 11:40:26.501 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 11:40:26.553 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 11:40:27.055 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 11:40:27.106 [Information] BackupService: Initializing backup service
2025-07-06 11:40:27.107 [Information] BackupService: Backup service initialized successfully
2025-07-06 11:40:27.158 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 11:40:27.158 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 11:40:27.160 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 11:40:27.161 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 11:40:27.212 [Information] BackupService: Getting predefined backup categories
2025-07-06 11:40:27.264 [Information] MainViewModel: Services initialized successfully
2025-07-06 11:40:27.267 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 11:40:27.270 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:40:27.273 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:40:27.273 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:40:27.274 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 11:40:44.369 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 11:40:44.370 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 11:40:44.378 [Warning] VocomArchitectureBridge: Device detection failed: Could not load file or assembly 'VolvoFlashWR.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
2025-07-06 11:40:44.379 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 11:40:44.379 [Information] MainViewModel: Found 0 Vocom device(s)
