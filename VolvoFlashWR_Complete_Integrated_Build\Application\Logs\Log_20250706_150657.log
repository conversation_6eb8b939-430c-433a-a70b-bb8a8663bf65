Log started at 7/6/2025 3:06:57 PM
2025-07-06 15:06:57.394 [Information] LoggingService: Logging service initialized
2025-07-06 15:06:57.417 [Information] App: Starting integrated application initialization
2025-07-06 15:06:57.420 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 15:06:57.425 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 15:06:57.427 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 15:06:57.428 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 15:06:57.431 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 15:06:57.434 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 15:06:57.438 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 15:06:57.449 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:06:57.452 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.456 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.457 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 15:06:57.461 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:06:57.465 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.468 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.469 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:06:57.473 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:06:57.476 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.479 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.480 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:06:57.485 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:06:57.489 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.492 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:06:57.492 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:06:57.495 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 15:06:57.498 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 15:06:57.498 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 15:06:57.500 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 15:06:57.501 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 15:06:57.501 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 15:06:57.502 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 15:06:57.502 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 15:06:57.503 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 15:06:57.504 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 15:06:57.504 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 15:06:57.505 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:06:57.505 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:06:57.506 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:06:57.514 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 15:06:57.515 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 15:06:57.516 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 15:06:57.523 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 15:06:57.524 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 15:06:57.526 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 15:06:57.528 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 15:06:57.533 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 15:06:57.535 [Information] LibraryExtractor: Copying system libraries
2025-07-06 15:06:57.543 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 15:06:57.552 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 15:09:16.311 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 15:11:38.634 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 15:12:57.832 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 15:13:48.697 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:14:42.599 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:15:35.162 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:16:28.230 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 15:16:28.231 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 15:16:28.231 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 15:16:28.232 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 15:16:28.233 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 15:16:28.234 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 15:16:28.244 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 15:16:28.248 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 15:16:28.250 [Information] DependencyManager: Initializing dependency manager
2025-07-06 15:16:28.251 [Information] DependencyManager: Setting up library search paths
2025-07-06 15:16:28.252 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:16:28.253 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 15:16:28.254 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 15:16:28.255 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 15:16:28.257 [Information] DependencyManager: Verifying required directories
2025-07-06 15:16:28.258 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:16:28.258 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 15:16:28.259 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 15:16:28.259 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 15:16:28.261 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 15:16:28.269 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 15:16:28.271 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 15:16:28.275 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 15:16:28.279 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 15:16:28.280 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 15:16:28.281 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:16:28.282 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:16:28.283 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:16:28.285 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 15:16:28.287 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 15:16:28.287 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 15:16:28.288 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 15:16:28.289 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 15:16:28.290 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 15:16:28.291 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 15:16:28.292 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 15:16:28.293 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 15:16:28.312 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 15:16:28.313 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 15:16:28.314 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 15:16:28.314 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 15:16:28.315 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 15:16:28.315 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 15:16:28.316 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 15:16:28.318 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 15:16:28.320 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 15:16:28.323 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 15:16:28.324 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 15:16:28.324 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 15:16:28.325 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 15:16:28.325 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 15:16:28.326 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 15:16:28.327 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 15:16:28.328 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 15:16:28.329 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 15:16:28.330 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 15:16:28.331 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 15:16:28.332 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 15:16:28.332 [Information] DependencyManager: Setting up environment variables
2025-07-06 15:16:28.333 [Information] DependencyManager: Environment variables configured
2025-07-06 15:16:28.335 [Information] DependencyManager: Verifying library loading status
2025-07-06 15:16:28.715 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 15:16:28.717 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 15:16:28.718 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 15:16:28.723 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 15:16:28.724 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 15:16:28.728 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 15:16:28.731 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 15:16:28.732 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 15:16:28.732 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 15:16:28.734 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 15:16:28.734 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 15:16:28.735 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:16:28.735 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 15:16:28.735 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 15:16:28.736 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 15:16:28.736 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 15:16:28.737 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 15:16:28.737 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 15:16:28.737 [Information] App: Integrated startup completed successfully
2025-07-06 15:16:28.743 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 15:16:28.763 [Information] App: Initializing application services
2025-07-06 15:16:28.765 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 15:16:28.766 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 15:16:28.825 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 15:16:28.826 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 15:16:28.828 [Information] App: Configuration service initialized successfully
2025-07-06 15:16:28.829 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 15:16:28.830 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 15:16:28.834 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 15:16:28.835 [Information] App: Final useDummyImplementations value: False
2025-07-06 15:16:28.836 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 15:16:28.838 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 15:16:28.857 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 15:16:28.859 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 15:16:28.859 [Information] App: usePatchedImplementation flag is: True
2025-07-06 15:16:28.859 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 15:16:28.860 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 15:16:28.860 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 15:16:28.861 [Information] App: verboseLogging flag is: True
2025-07-06 15:16:28.863 [Information] App: Verifying real hardware requirements...
2025-07-06 15:16:28.863 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 15:16:28.864 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 15:16:28.864 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 15:16:28.864 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 15:16:28.865 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 15:16:28.865 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 15:16:28.866 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 15:16:28.866 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 15:16:28.878 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 15:16:28.881 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 15:16:28.883 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 15:16:28.886 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 15:16:28.887 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 15:16:28.888 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 15:16:28.890 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 15:16:28.890 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 15:16:28.890 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 15:16:28.891 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 15:16:28.891 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 15:16:28.894 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 15:16:28.895 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 15:16:28.896 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 15:16:28.899 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 15:16:28.900 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 15:16:28.900 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 15:16:28.901 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 15:16:28.903 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 15:16:28.904 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 15:16:28.906 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 15:16:28.908 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 15:16:28.912 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 15:16:28.923 [Information] VocomArchitectureBridge: Started bridge process with PID 11972
2025-07-06 15:16:29.925 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 15:16:29.929 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 15:16:30.049 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 15:16:30.050 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 15:16:30.050 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 15:16:30.051 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 15:16:30.051 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 15:16:30.052 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 15:16:30.052 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 15:16:30.052 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 15:16:30.053 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 15:16:30.053 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 15:16:30.055 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 15:16:30.117 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:16:30.178 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:16:30.179 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:16:30.180 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 15:16:30.184 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 15:16:30.190 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:30.191 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 15:16:30.195 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 15:16:30.197 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 15:16:30.198 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 15:16:30.201 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 15:16:30.204 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 15:16:30.209 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 15:16:30.212 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 15:16:30.215 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 15:16:30.226 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:16:30.231 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:16:30.232 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:30.235 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:16:30.236 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:16:30.236 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:30.240 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:16:30.241 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:16:30.241 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:30.245 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:16:30.246 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:16:30.246 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:30.249 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:16:30.250 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:16:30.250 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:30.251 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 15:16:30.255 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 15:16:30.257 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:30.258 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 15:16:30.258 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 15:16:30.259 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:30.259 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 15:16:30.259 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 15:16:30.260 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:30.260 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 15:16:30.261 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 15:16:30.261 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:30.262 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 15:16:30.262 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 15:16:30.262 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 15:16:30.264 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:16:30.269 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:16:30.269 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:16:30.271 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 15:16:30.271 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 15:16:30.273 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 15:16:31.273 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 15:16:31.274 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 15:16:31.275 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 15:16:31.275 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 15:16:31.276 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 15:16:31.276 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 15:16:31.278 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 15:16:31.279 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 15:16:31.280 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 15:16:31.280 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:16:31.281 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:16:31.282 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:31.282 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:16:31.282 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:16:31.283 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:31.283 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:16:31.284 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:16:31.284 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:31.284 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:16:31.285 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:16:31.285 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:31.285 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:16:31.286 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:16:31.286 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:31.287 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 15:16:31.287 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 15:16:31.288 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:31.288 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 15:16:31.289 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 15:16:31.289 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:31.290 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 15:16:31.290 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 15:16:31.291 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:31.291 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 15:16:31.291 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 15:16:31.292 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:31.292 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 15:16:31.292 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 15:16:31.292 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 15:16:31.293 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:16:31.296 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:16:31.296 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:16:31.296 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 15:16:31.297 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 15:16:31.297 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 15:16:33.297 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 15:16:33.299 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 15:16:33.299 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 15:16:33.299 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 15:16:33.300 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 15:16:33.301 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 15:16:33.302 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 15:16:33.302 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 15:16:33.304 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 15:16:33.306 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:16:33.308 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 15:16:33.308 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:33.309 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:16:33.309 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 15:16:33.309 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:33.310 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:16:33.310 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 15:16:33.310 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:33.311 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:16:33.311 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 15:16:33.311 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:33.311 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:16:33.312 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 15:16:33.312 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 15:16:33.312 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 15:16:33.313 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 15:16:33.313 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:33.313 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 15:16:33.314 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 15:16:33.314 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:33.314 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 15:16:33.315 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 15:16:33.315 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:33.316 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 15:16:33.316 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 15:16:33.316 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 15:16:33.317 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 15:16:33.317 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 15:16:33.317 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 15:16:33.317 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:16:33.319 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:16:33.319 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:16:33.319 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 15:16:33.320 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 15:16:33.320 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 15:16:36.321 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 15:16:36.321 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 15:16:36.322 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 15:16:36.324 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 15:16:36.826 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 15:16:36.827 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 15:16:36.828 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 15:16:36.828 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 15:16:36.833 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 15:16:36.835 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 15:16:36.838 [Information] BackupService: Initializing backup service
2025-07-06 15:16:36.839 [Information] BackupService: Backup service initialized successfully
2025-07-06 15:16:36.839 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 15:16:36.840 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 15:16:36.844 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 15:16:36.870 [Information] BackupService: Compressing backup data
2025-07-06 15:16:36.881 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 15:16:36.882 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 15:16:36.883 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 15:16:36.883 [Information] BackupService: Compressing backup data
2025-07-06 15:16:36.885 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-06 15:16:36.885 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 15:16:36.885 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 15:16:36.886 [Information] BackupService: Compressing backup data
2025-07-06 15:16:36.887 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-06 15:16:36.887 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 15:16:36.888 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 15:16:36.888 [Information] BackupService: Compressing backup data
2025-07-06 15:16:36.889 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-06 15:16:36.890 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 15:16:36.890 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 15:16:36.891 [Information] BackupService: Compressing backup data
2025-07-06 15:16:36.893 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-06 15:16:36.894 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 15:16:36.894 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 15:16:36.895 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 15:16:36.895 [Information] BackupService: Compressing backup data
2025-07-06 15:16:36.896 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-07-06 15:16:36.897 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 15:16:36.897 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 15:16:36.900 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 15:16:36.905 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 15:16:36.925 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 15:16:36.997 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 15:16:36.998 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 15:16:37.000 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 15:16:37.000 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 15:16:37.004 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 15:16:37.006 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 15:16:37.008 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 15:16:37.012 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 15:16:37.012 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 15:16:37.025 [Information] LicensingService: Initializing licensing service
2025-07-06 15:16:37.084 [Information] LicensingService: License information loaded successfully
2025-07-06 15:16:37.092 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 15:16:37.092 [Information] App: Licensing service initialized successfully
2025-07-06 15:16:37.093 [Information] App: License status: Trial
2025-07-06 15:16:37.093 [Information] App: Trial period: 29 days remaining
2025-07-06 15:16:37.094 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 15:16:37.332 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 15:16:37.333 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 15:16:37.333 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 15:16:37.333 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 15:16:37.384 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 15:16:37.886 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 15:16:37.938 [Information] BackupService: Initializing backup service
2025-07-06 15:16:37.939 [Information] BackupService: Backup service initialized successfully
2025-07-06 15:16:37.992 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 15:16:37.993 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 15:16:37.997 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 15:16:37.997 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 15:16:38.051 [Information] BackupService: Getting predefined backup categories
2025-07-06 15:16:38.103 [Information] MainViewModel: Services initialized successfully
2025-07-06 15:16:38.107 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 15:16:38.110 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 15:16:38.129 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 15:16:38.130 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 15:16:38.131 [Information] MainViewModel: Found 0 Vocom device(s)
