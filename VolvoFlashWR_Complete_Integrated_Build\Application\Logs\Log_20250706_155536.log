Log started at 7/6/2025 3:55:36 PM
2025-07-06 15:55:36.918 [Information] LoggingService: Logging service initialized
2025-07-06 15:55:36.938 [Information] App: Starting integrated application initialization
2025-07-06 15:55:36.940 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 15:55:36.944 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 15:55:36.948 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 15:55:36.949 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 15:55:36.951 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 15:55:36.954 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 15:55:36.958 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 15:55:36.970 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:55:36.974 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:36.978 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:36.980 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 15:55:36.984 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:55:36.988 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:36.991 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:36.992 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:55:36.997 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:55:37.001 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:37.004 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:37.005 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:55:37.010 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 15:55:37.014 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:37.018 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 15:55:37.019 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:55:37.022 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 15:55:37.026 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 15:55:37.026 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 15:55:37.029 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 15:55:37.029 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 15:55:37.030 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 15:55:37.031 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 15:55:37.031 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 15:55:37.032 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 15:55:37.033 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 15:55:37.034 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 15:55:37.034 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 15:55:37.035 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 15:55:37.035 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 15:55:37.045 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 15:55:37.046 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 15:55:37.047 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 15:55:37.055 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 15:55:37.056 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 15:55:37.059 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 15:55:37.062 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 15:55:37.067 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 15:55:37.070 [Information] LibraryExtractor: Copying system libraries
2025-07-06 15:55:37.080 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 15:55:37.093 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 15:56:03.915 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 15:56:55.223 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 15:58:14.135 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 15:59:24.685 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:00:18.394 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:01:06.643 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:02:07.907 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 16:02:07.908 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 16:02:07.909 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 16:02:07.909 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 16:02:07.910 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 16:02:07.911 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 16:02:07.917 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 16:02:07.922 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 16:02:07.924 [Information] DependencyManager: Initializing dependency manager
2025-07-06 16:02:07.926 [Information] DependencyManager: Setting up library search paths
2025-07-06 16:02:07.929 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:02:07.930 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:02:07.930 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 16:02:07.931 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 16:02:07.933 [Information] DependencyManager: Verifying required directories
2025-07-06 16:02:07.933 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:02:07.934 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:02:07.934 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 16:02:07.935 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 16:02:07.937 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 16:02:07.948 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 16:02:07.950 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 16:02:07.955 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 16:02:07.959 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 16:02:07.962 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 16:02:07.963 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 16:02:07.964 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 16:02:07.965 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 16:02:07.967 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 16:02:07.969 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 16:02:07.970 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 16:02:07.970 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 16:02:07.971 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 16:02:07.971 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 16:02:07.972 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 16:02:07.973 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 16:02:07.974 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 16:02:07.974 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 16:02:07.975 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 16:02:07.976 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 16:02:07.977 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 16:02:07.977 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 16:02:07.979 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 16:02:07.979 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 16:02:07.981 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 16:02:07.981 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 16:02:07.982 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 16:02:07.983 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 16:02:07.983 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 16:02:07.984 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 16:02:07.985 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 16:02:07.985 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 16:02:07.987 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 16:02:07.990 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 16:02:07.991 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 16:02:07.992 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 16:02:07.994 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 16:02:07.996 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 16:02:07.997 [Information] DependencyManager: Setting up environment variables
2025-07-06 16:02:07.999 [Information] DependencyManager: Environment variables configured
2025-07-06 16:02:08.001 [Information] DependencyManager: Verifying library loading status
2025-07-06 16:02:08.476 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 16:02:08.477 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 16:02:08.479 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 16:02:08.483 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 16:02:08.485 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 16:02:08.489 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 16:02:08.492 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 16:02:08.493 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 16:02:08.495 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 16:02:08.497 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 16:02:08.498 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 16:02:08.499 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:02:08.499 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 16:02:08.500 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 16:02:08.500 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 16:02:08.501 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 16:02:08.501 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 16:02:08.501 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 16:02:08.502 [Information] App: Integrated startup completed successfully
2025-07-06 16:02:08.507 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 16:02:08.529 [Information] App: Initializing application services
2025-07-06 16:02:08.532 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 16:02:08.533 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 16:02:08.584 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 16:02:08.585 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 16:02:08.586 [Information] App: Configuration service initialized successfully
2025-07-06 16:02:08.588 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 16:02:08.588 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 16:02:08.596 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 16:02:08.596 [Information] App: Final useDummyImplementations value: False
2025-07-06 16:02:08.597 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 16:02:08.599 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 16:02:08.624 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 16:02:08.626 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 16:02:08.629 [Information] App: usePatchedImplementation flag is: True
2025-07-06 16:02:08.630 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 16:02:08.631 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 16:02:08.631 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 16:02:08.631 [Information] App: verboseLogging flag is: True
2025-07-06 16:02:08.636 [Information] App: Verifying real hardware requirements...
2025-07-06 16:02:08.637 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 16:02:08.637 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 16:02:08.638 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 16:02:08.638 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 16:02:08.639 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 16:02:08.639 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 16:02:08.640 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 16:02:08.640 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 16:02:08.651 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 16:02:08.656 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 16:02:08.659 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 16:02:08.668 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 16:02:08.668 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 16:02:08.669 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 16:02:08.670 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 16:02:08.670 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 16:02:08.671 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 16:02:08.671 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 16:02:08.671 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 16:02:08.674 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 16:02:08.676 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 16:02:08.697 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 16:02:08.707 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 16:02:08.708 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 16:02:08.708 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 16:02:08.709 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 16:02:08.717 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 16:02:08.718 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 16:02:08.720 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 16:02:08.726 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:02:08.734 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:02:08.750 [Information] VocomArchitectureBridge: Started bridge process with PID 16436
2025-07-06 16:02:09.752 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 16:02:09.759 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 16:02:09.903 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 16:02:09.904 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:02:09.905 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 16:02:09.905 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 16:02:09.906 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:02:09.906 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:02:09.906 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 16:02:09.907 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:02:09.907 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 16:02:09.908 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 16:02:09.908 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 16:02:09.982 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:02:10.043 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:02:10.045 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:02:10.046 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 16:02:10.050 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 16:02:10.054 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:10.054 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 16:02:10.059 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:02:10.064 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:02:10.064 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:02:10.069 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:02:10.071 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:02:10.075 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:02:10.081 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:02:10.084 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:02:10.093 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:02:10.099 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:02:10.100 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:10.104 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:02:10.105 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:02:10.105 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:10.107 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:02:10.108 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:02:10.108 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:10.111 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:02:10.112 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:02:10.113 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:10.116 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:02:10.116 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:02:10.117 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:10.117 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:02:10.122 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:02:10.124 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:10.125 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:02:10.125 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:02:10.126 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:10.126 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:02:10.126 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:02:10.127 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:10.128 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:02:10.129 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:02:10.129 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:10.130 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:02:10.130 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:02:10.131 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:02:10.133 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:02:10.134 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:02:10.135 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:02:10.136 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:02:10.136 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:02:10.138 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 16:02:11.139 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 16:02:11.140 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:02:11.140 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:02:11.141 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:02:11.142 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:02:11.142 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:02:11.144 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:02:11.145 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:02:11.147 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:02:11.148 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:02:11.148 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:02:11.149 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:11.149 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:02:11.149 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:02:11.150 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:11.150 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:02:11.150 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:02:11.151 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:11.151 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:02:11.152 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:02:11.152 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:11.152 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:02:11.153 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:02:11.153 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:11.153 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:02:11.154 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:02:11.154 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:11.155 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:02:11.155 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:02:11.155 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:11.156 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:02:11.156 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:02:11.156 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:11.157 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:02:11.157 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:02:11.157 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:11.158 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:02:11.158 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:02:11.158 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:02:11.159 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:02:11.160 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:02:11.160 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:02:11.161 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:02:11.161 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:02:11.161 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 16:02:13.162 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 16:02:13.163 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 16:02:13.163 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 16:02:13.164 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 16:02:13.164 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 16:02:13.165 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 16:02:13.166 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 16:02:13.167 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 16:02:13.168 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 16:02:13.168 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:02:13.169 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 16:02:13.169 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:13.169 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:02:13.170 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 16:02:13.170 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:13.170 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:02:13.170 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 16:02:13.171 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:13.171 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:02:13.171 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 16:02:13.172 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:13.172 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:02:13.172 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 16:02:13.173 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 16:02:13.173 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 16:02:13.173 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 16:02:13.174 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:13.174 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 16:02:13.174 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 16:02:13.175 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:13.175 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 16:02:13.175 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 16:02:13.176 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:13.176 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 16:02:13.176 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 16:02:13.177 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 16:02:13.177 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 16:02:13.177 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 16:02:13.178 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 16:02:13.179 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:02:13.182 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:02:13.182 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:02:13.183 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 16:02:13.183 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 16:02:13.183 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 16:02:16.185 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 16:02:16.185 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 16:02:16.187 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 16:02:16.189 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 16:02:16.690 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 16:02:16.691 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 16:02:16.692 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 16:02:16.693 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 16:02:16.700 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 16:02:16.703 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 16:02:16.706 [Information] BackupService: Initializing backup service
2025-07-06 16:02:16.707 [Information] BackupService: Backup service initialized successfully
2025-07-06 16:02:16.707 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 16:02:16.709 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 16:02:16.712 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 16:02:16.741 [Information] BackupService: Compressing backup data
2025-07-06 16:02:16.752 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-06 16:02:16.754 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 16:02:16.756 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 16:02:16.757 [Information] BackupService: Compressing backup data
2025-07-06 16:02:16.758 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-06 16:02:16.759 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 16:02:16.759 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 16:02:16.760 [Information] BackupService: Compressing backup data
2025-07-06 16:02:16.761 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (444 bytes)
2025-07-06 16:02:16.762 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 16:02:16.762 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 16:02:16.762 [Information] BackupService: Compressing backup data
2025-07-06 16:02:16.767 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-06 16:02:16.768 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 16:02:16.768 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 16:02:16.769 [Information] BackupService: Compressing backup data
2025-07-06 16:02:16.773 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-07-06 16:02:16.773 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 16:02:16.774 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 16:02:16.774 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 16:02:16.776 [Information] BackupService: Compressing backup data
2025-07-06 16:02:16.778 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-06 16:02:16.778 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 16:02:16.779 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 16:02:16.783 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 16:02:16.787 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 16:02:16.804 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 16:02:16.880 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 16:02:16.882 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 16:02:16.884 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 16:02:16.884 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 16:02:16.885 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 16:02:16.886 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 16:02:16.887 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 16:02:16.892 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 16:02:16.893 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 16:02:16.907 [Information] LicensingService: Initializing licensing service
2025-07-06 16:02:16.996 [Information] LicensingService: License information loaded successfully
2025-07-06 16:02:17.002 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 16:02:17.002 [Information] App: Licensing service initialized successfully
2025-07-06 16:02:17.003 [Information] App: License status: Trial
2025-07-06 16:02:17.004 [Information] App: Trial period: 29 days remaining
2025-07-06 16:02:17.005 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 16:02:17.262 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 16:02:17.262 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 16:02:17.262 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 16:02:17.263 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 16:02:17.314 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 16:02:17.816 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 16:02:17.867 [Information] BackupService: Initializing backup service
2025-07-06 16:02:17.868 [Information] BackupService: Backup service initialized successfully
2025-07-06 16:02:17.919 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 16:02:17.920 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 16:02:17.922 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 16:02:17.923 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 16:02:17.975 [Information] BackupService: Getting predefined backup categories
2025-07-06 16:02:18.028 [Information] MainViewModel: Services initialized successfully
2025-07-06 16:02:18.032 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:02:18.034 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:02:18.037 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:02:18.038 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:02:18.039 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 16:04:35.252 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 16:04:35.253 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 16:04:35.261 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 16:04:35.261 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 16:04:35.262 [Information] MainViewModel: Found 0 Vocom device(s)
