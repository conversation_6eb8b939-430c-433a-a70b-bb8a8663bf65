using System;
using System.IO;
using System.Runtime.InteropServices;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom.ArchitectureBridge
{
    /// <summary>
    /// Factory that creates the appropriate Vocom service based on architecture compatibility
    /// Automatically handles the x64/x86 mismatch by choosing the right implementation
    /// </summary>
    public class ArchitectureAwareVocomServiceFactory
    {
        private readonly ILoggingService _logger;

        public ArchitectureAwareVocomServiceFactory(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates the most appropriate Vocom service based on current architecture and library availability
        /// </summary>
        public IVocomService CreateVocomService()
        {
            try
            {
                _logger.LogInformation("=== Architecture-Aware Vocom Service Factory ===", "ArchitectureAwareVocomServiceFactory");
                
                // Check current process architecture
                bool is64BitProcess = Environment.Is64BitProcess;
                string processArch = is64BitProcess ? "x64" : "x86";
                _logger.LogInformation($"Current process architecture: {processArch}", "ArchitectureAwareVocomServiceFactory");

                // Check library architecture compatibility
                var compatibilityResult = CheckLibraryCompatibility();
                _logger.LogInformation($"Library compatibility check: {compatibilityResult}", "ArchitectureAwareVocomServiceFactory");

                // Decide which service to create based on compatibility
                if (compatibilityResult == LibraryCompatibility.Compatible)
                {
                    _logger.LogInformation("Libraries are compatible - using direct Vocom service", "ArchitectureAwareVocomServiceFactory");
                    return CreateDirectVocomService();
                }
                else if (compatibilityResult == LibraryCompatibility.ArchitectureMismatch)
                {
                    _logger.LogInformation("Architecture mismatch detected - using bridged Vocom service", "ArchitectureAwareVocomServiceFactory");
                    return CreateBridgedVocomService();
                }
                else
                {
                    _logger.LogWarning("Libraries missing or incompatible - using dummy Vocom service", "ArchitectureAwareVocomServiceFactory");
                    return CreateDummyVocomService();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception in service factory: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                _logger.LogWarning("Falling back to dummy Vocom service due to exception", "ArchitectureAwareVocomServiceFactory");
                return CreateDummyVocomService();
            }
        }

        /// <summary>
        /// Checks the compatibility of Phoenix APCI libraries with current process architecture
        /// </summary>
        private LibraryCompatibility CheckLibraryCompatibility()
        {
            try
            {
                bool is64BitProcess = Environment.Is64BitProcess;
                var appPath = AppDomain.CurrentDomain.BaseDirectory;
                var librariesPath = Path.Combine(appPath, "Libraries");

                // Critical libraries to check
                string[] criticalLibraries = {
                    "apci.dll",
                    "Volvo.ApciPlus.dll",
                    "WUDFPuma.dll"
                };

                int compatibleCount = 0;
                int incompatibleCount = 0;
                int missingCount = 0;

                foreach (string library in criticalLibraries)
                {
                    string libraryPath = Path.Combine(librariesPath, library);
                    
                    if (!File.Exists(libraryPath))
                    {
                        _logger.LogWarning($"Critical library missing: {library}", "ArchitectureAwareVocomServiceFactory");
                        missingCount++;
                        continue;
                    }

                    var archCompatibility = CheckLibraryArchitecture(libraryPath, is64BitProcess);
                    if (archCompatibility)
                    {
                        compatibleCount++;
                        _logger.LogInformation($"✓ Compatible library: {library}", "ArchitectureAwareVocomServiceFactory");
                    }
                    else
                    {
                        incompatibleCount++;
                        _logger.LogWarning($"✗ Architecture mismatch: {library}", "ArchitectureAwareVocomServiceFactory");
                    }
                }

                // Determine overall compatibility
                if (missingCount > 0)
                {
                    _logger.LogWarning($"Missing {missingCount} critical libraries", "ArchitectureAwareVocomServiceFactory");
                    return LibraryCompatibility.Missing;
                }

                if (incompatibleCount > 0)
                {
                    _logger.LogWarning($"Found {incompatibleCount} incompatible libraries", "ArchitectureAwareVocomServiceFactory");
                    return LibraryCompatibility.ArchitectureMismatch;
                }

                if (compatibleCount == criticalLibraries.Length)
                {
                    _logger.LogInformation("All critical libraries are compatible", "ArchitectureAwareVocomServiceFactory");
                    return LibraryCompatibility.Compatible;
                }

                return LibraryCompatibility.Unknown;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during compatibility check: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return LibraryCompatibility.Unknown;
            }
        }

        /// <summary>
        /// Checks if a library's architecture matches the current process architecture
        /// </summary>
        private bool CheckLibraryArchitecture(string libraryPath, bool is64BitProcess)
        {
            try
            {
                using var fileStream = new FileStream(libraryPath, FileMode.Open, FileAccess.Read);
                using var reader = new BinaryReader(fileStream);

                // Check DOS header
                if (reader.ReadUInt16() != 0x5A4D) // "MZ"
                    return false;

                // Jump to PE header
                fileStream.Seek(60, SeekOrigin.Begin);
                int peHeaderOffset = reader.ReadInt32();
                fileStream.Seek(peHeaderOffset, SeekOrigin.Begin);

                // Check PE signature
                if (reader.ReadUInt32() != 0x00004550) // "PE\0\0"
                    return false;

                // Read machine type
                ushort machineType = reader.ReadUInt16();

                // Determine compatibility
                bool is64BitLibrary = (machineType == 0x8664); // IMAGE_FILE_MACHINE_AMD64
                bool is32BitLibrary = (machineType == 0x014c); // IMAGE_FILE_MACHINE_I386

                return (is64BitProcess && is64BitLibrary) || (!is64BitProcess && is32BitLibrary);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error checking architecture for {libraryPath}: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return false;
            }
        }

        /// <summary>
        /// Creates a direct Vocom service (for compatible architectures)
        /// </summary>
        private IVocomService CreateDirectVocomService()
        {
            try
            {
                // Use the existing patched Vocom service factory
                var patchedFactory = new PatchedVocomServiceFactory(_logger);
                var serviceTask = patchedFactory.CreateServiceAsync();
                serviceTask.Wait(); // Wait for the async operation to complete
                return serviceTask.Result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create direct Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                return CreateDummyVocomService();
            }
        }

        /// <summary>
        /// Creates a bridged Vocom service (for architecture mismatches)
        /// </summary>
        private IVocomService CreateBridgedVocomService()
        {
            try
            {
                _logger.LogInformation("Architecture mismatch detected - creating bridged Vocom service", "ArchitectureAwareVocomServiceFactory");

                // Create the bridged service that uses the architecture bridge
                var bridgedService = new BridgedVocomService(_logger);

                // Initialize the service asynchronously
                var initTask = bridgedService.InitializeAsync();
                initTask.Wait(); // Wait for initialization to complete

                if (initTask.Result)
                {
                    _logger.LogInformation("Bridged Vocom service created and initialized successfully", "ArchitectureAwareVocomServiceFactory");
                    return bridgedService;
                }
                else
                {
                    _logger.LogWarning("Bridged Vocom service initialization failed, falling back to dummy mode", "ArchitectureAwareVocomServiceFactory");
                    return CreateDummyVocomService();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create bridged Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                _logger.LogWarning("Falling back to dummy mode due to bridge service failure", "ArchitectureAwareVocomServiceFactory");
                return CreateDummyVocomService();
            }
        }

        /// <summary>
        /// Creates a dummy Vocom service (fallback)
        /// </summary>
        private IVocomService CreateDummyVocomService()
        {
            try
            {
                return new DummyVocomService(_logger);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to create dummy Vocom service: {ex.Message}", "ArchitectureAwareVocomServiceFactory");
                throw new InvalidOperationException("Unable to create any Vocom service implementation", ex);
            }
        }
    }

    /// <summary>
    /// Enumeration of library compatibility states
    /// </summary>
    public enum LibraryCompatibility
    {
        Compatible,
        ArchitectureMismatch,
        Missing,
        Unknown
    }
}
