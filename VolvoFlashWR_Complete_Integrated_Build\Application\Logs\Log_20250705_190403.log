Log started at 7/5/2025 7:04:03 PM
2025-07-05 19:04:03.287 [Information] LoggingService: Logging service initialized
2025-07-05 19:04:03.300 [Information] App: Starting integrated application initialization
2025-07-05 19:04:03.302 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 19:04:03.304 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 19:04:03.306 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 19:04:03.306 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 19:04:03.308 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 19:04:03.309 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 19:04:03.313 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 19:04:03.321 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 19:04:03.323 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.326 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.327 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 19:04:03.331 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 19:04:03.333 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.334 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.335 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 19:04:03.337 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 19:04:03.338 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.341 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.342 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 19:04:03.345 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 19:04:03.347 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.349 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 19:04:03.350 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 19:04:03.351 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 19:04:03.353 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 19:04:03.353 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 19:04:03.354 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 19:04:03.355 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 19:04:03.355 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 19:04:03.355 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 19:04:03.356 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 19:04:03.356 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 19:04:03.357 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 19:04:03.357 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 19:04:03.358 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 19:04:03.358 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 19:04:03.358 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 19:04:03.364 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 19:04:03.364 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 19:04:03.364 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 19:04:03.369 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 19:04:03.369 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 19:04:03.370 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 19:04:03.372 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 19:04:03.374 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 19:04:03.377 [Information] LibraryExtractor: Copying system libraries
2025-07-05 19:04:03.383 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 19:04:03.390 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 19:04:30.441 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 19:05:18.932 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 19:06:03.906 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-05 19:06:49.920 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 19:07:35.792 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 19:08:22.532 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 19:09:09.789 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 19:09:09.790 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 19:09:09.790 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 19:09:09.790 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 19:09:09.790 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 19:09:09.791 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 19:09:09.794 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 19:09:09.795 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 19:09:09.797 [Information] DependencyManager: Initializing dependency manager
2025-07-05 19:09:09.798 [Information] DependencyManager: Setting up library search paths
2025-07-05 19:09:09.800 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 19:09:09.800 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 19:09:09.801 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 19:09:09.801 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 19:09:09.802 [Information] DependencyManager: Verifying required directories
2025-07-05 19:09:09.803 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 19:09:09.803 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 19:09:09.803 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 19:09:09.803 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 19:09:09.804 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 19:09:09.819 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 19:09:09.825 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 19:09:09.827 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 19:09:09.832 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 19:09:09.834 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 19:09:09.835 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 19:09:09.835 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 19:09:09.836 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 19:09:09.837 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 19:09:09.838 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-05 19:09:09.839 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 19:09:09.839 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-05 19:09:09.840 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-05 19:09:09.840 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-05 19:09:09.841 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-05 19:09:09.841 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 19:09:09.842 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-05 19:09:09.842 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-05 19:09:09.843 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-05 19:09:09.844 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-05 19:09:09.844 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 19:09:09.844 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-05 19:09:09.845 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-05 19:09:09.845 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-05 19:09:09.846 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-05 19:09:09.847 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 19:09:09.847 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-05 19:09:09.848 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-05 19:09:09.848 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-05 19:09:09.849 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-05 19:09:09.850 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-05 19:09:09.850 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 19:09:09.851 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-05 19:09:09.852 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-05 19:09:09.852 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-05 19:09:09.853 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 19:09:09.853 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-05 19:09:09.854 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-05 19:09:09.854 [Information] DependencyManager: Setting up environment variables
2025-07-05 19:09:09.854 [Information] DependencyManager: Environment variables configured
2025-07-05 19:09:09.856 [Information] DependencyManager: Verifying library loading status
2025-07-05 19:09:10.180 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-05 19:09:10.180 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-05 19:09:10.181 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 19:09:10.183 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 19:09:10.185 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 19:09:10.188 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 19:09:10.189 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 19:09:10.190 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 19:09:10.190 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 19:09:10.191 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 19:09:10.191 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 19:09:10.192 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 19:09:10.192 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 19:09:10.192 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 19:09:10.192 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 19:09:10.192 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 19:09:10.193 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 19:09:10.193 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 19:09:10.193 [Information] App: Integrated startup completed successfully
2025-07-05 19:09:10.196 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 19:09:10.212 [Information] App: Initializing application services
2025-07-05 19:09:10.213 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 19:09:10.214 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 19:09:10.254 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 19:09:10.255 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 19:09:10.256 [Information] App: Configuration service initialized successfully
2025-07-05 19:09:10.257 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 19:09:10.258 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 19:09:10.261 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 19:09:10.262 [Information] App: Final useDummyImplementations value: False
2025-07-05 19:09:10.262 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 19:09:10.263 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-05 19:09:10.277 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 19:09:10.278 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 19:09:10.278 [Information] App: usePatchedImplementation flag is: True
2025-07-05 19:09:10.278 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 19:09:10.278 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 19:09:10.279 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 19:09:10.279 [Information] App: verboseLogging flag is: True
2025-07-05 19:09:10.280 [Information] App: Verifying real hardware requirements...
2025-07-05 19:09:10.281 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 19:09:10.281 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 19:09:10.282 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 19:09:10.282 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 19:09:10.282 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 19:09:10.283 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 19:09:10.284 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 19:09:10.284 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 19:09:10.296 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 19:09:10.298 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 19:09:10.301 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 19:09:10.304 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 19:09:10.304 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 19:09:10.304 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 19:09:10.305 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 19:09:10.305 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 19:09:10.305 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 19:09:10.306 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 19:09:10.306 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 19:09:10.306 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 19:09:10.307 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 19:09:10.308 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-05 19:09:10.310 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-05 19:09:10.310 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-05 19:09:10.310 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-05 19:09:10.311 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-05 19:09:10.312 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 19:09:10.313 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 19:09:10.314 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-05 19:09:10.316 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-05 19:09:10.320 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-05 19:09:10.423 [Information] VocomArchitectureBridge: Started bridge process with PID 5548
2025-07-05 19:09:11.426 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-05 19:09:11.438 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-05 19:09:11.576 [Error] VocomArchitectureBridge: Bridge initialization failed: Bridge service initialization failed
2025-07-05 19:09:11.577 [Warning] BridgedVocomService: Bridge initialization failed - service will operate in limited mode
2025-07-05 19:09:11.578 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-05 19:09:11.578 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 19:09:11.578 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-05 19:09:11.578 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-05 19:09:11.579 [Error] VocomArchitectureBridge: Exception during bridge initialization: All pipe instances are busy.
2025-07-05 19:09:11.579 [Warning] BridgedVocomService: Bridge initialization failed - service will operate in limited mode
2025-07-05 19:09:11.579 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-05 19:09:11.580 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-05 19:09:11.580 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 19:09:11.622 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-05 19:09:11.622 [Warning] BridgedVocomService: Bridge not available - returning simulated device for testing
2025-07-05 19:09:11.623 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 19:09:11.624 [Information] BridgedVocomService: Connecting to Vocom device SIMULATED_VOCOM_001 through bridge
2025-07-05 19:09:11.625 [Warning] BridgedVocomService: Bridge not available - simulating connection for testing
2025-07-05 19:09:11.625 [Information] BridgedVocomService: Simulated connection to device SIMULATED_VOCOM_001 (bridge not available)
2025-07-05 19:09:11.625 [Information] App: Connected to Vocom device Simulated Vocom Device
2025-07-05 19:09:11.628 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 19:09:11.632 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:11.632 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 19:09:11.637 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 19:09:11.639 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 19:09:11.639 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 19:09:11.642 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 19:09:11.644 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 19:09:11.647 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 19:09:11.651 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 19:09:11.654 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 19:09:11.660 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 19:09:11.663 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 19:09:11.664 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:11.668 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 19:09:11.669 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 19:09:11.669 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:11.672 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 19:09:11.672 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 19:09:11.673 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:11.675 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 19:09:11.676 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 19:09:11.676 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:11.678 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 19:09:11.679 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 19:09:11.679 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:11.679 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 19:09:11.683 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 19:09:11.686 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:11.686 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 19:09:11.686 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 19:09:11.686 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:11.687 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 19:09:11.687 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 19:09:11.687 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:11.687 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 19:09:11.688 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 19:09:11.688 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:11.688 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 19:09:11.689 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 19:09:11.689 [Information] ECUCommunicationService: Attempting to reconnect to the Vocom adapter
2025-07-05 19:09:11.691 [Information] BridgedVocomService: Disconnecting from Vocom device through bridge
2025-07-05 19:09:11.691 [Information] ECUCommunicationService: Vocom device disconnected: 
2025-07-05 19:09:11.694 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 19:09:11.695 [Error] ECUCommunicationService: ECU communication service is not initialized
2025-07-05 19:09:11.695 [Information] BridgedVocomService: Successfully disconnected from Vocom device
2025-07-05 19:09:11.696 [Error] BridgedVocomService: Device parameter is null
2025-07-05 19:09:11.696 [Error] ECUCommunicationService: Failed to reconnect to the Vocom adapter
2025-07-05 19:09:11.697 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-05 19:09:12.698 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-05 19:09:12.699 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 19:09:12.701 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 19:09:12.702 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 19:09:12.703 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 19:09:12.703 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 19:09:12.706 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 19:09:12.706 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 19:09:12.708 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 19:09:12.709 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 19:09:12.710 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 19:09:12.711 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:12.711 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 19:09:12.712 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 19:09:12.712 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:12.713 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 19:09:12.714 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 19:09:12.714 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:12.715 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 19:09:12.716 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 19:09:12.716 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:12.717 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 19:09:12.718 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 19:09:12.719 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:12.719 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 19:09:12.720 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 19:09:12.721 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:12.722 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 19:09:12.722 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 19:09:12.723 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:12.728 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 19:09:12.729 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 19:09:12.730 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:12.731 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 19:09:12.731 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 19:09:12.732 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:12.733 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 19:09:12.733 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 19:09:12.735 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-05 19:09:12.739 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-05 19:09:12.739 [Warning] BridgedVocomService: Bridge not available - returning simulated device for testing
2025-07-05 19:09:12.740 [Information] BridgedVocomService: Connecting to Vocom device SIMULATED_VOCOM_001 through bridge
2025-07-05 19:09:12.740 [Warning] BridgedVocomService: Bridge not available - simulating connection for testing
2025-07-05 19:09:12.741 [Information] ECUCommunicationService: Vocom device connected: 
2025-07-05 19:09:12.741 [Information] ECUCommunicationService: Vocom device connected: 
2025-07-05 19:09:12.741 [Information] BridgedVocomService: Simulated connection to device SIMULATED_VOCOM_001 (bridge not available)
2025-07-05 19:09:12.742 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 19:09:12.742 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 19:09:12.744 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-05 19:09:12.744 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-05 19:09:12.750 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 19:09:12.753 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 19:09:12.758 [Information] BackupService: Initializing backup service
2025-07-05 19:09:12.758 [Information] BackupService: Backup service initialized successfully
2025-07-05 19:09:12.758 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 19:09:12.759 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 19:09:12.762 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 19:09:12.800 [Information] BackupService: Compressing backup data
2025-07-05 19:09:12.808 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-07-05 19:09:12.810 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 19:09:12.810 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 19:09:12.810 [Information] BackupService: Compressing backup data
2025-07-05 19:09:12.811 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (452 bytes)
2025-07-05 19:09:12.812 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 19:09:12.812 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 19:09:12.813 [Information] BackupService: Compressing backup data
2025-07-05 19:09:12.814 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-05 19:09:12.814 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 19:09:12.814 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 19:09:12.815 [Information] BackupService: Compressing backup data
2025-07-05 19:09:12.816 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-05 19:09:12.816 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 19:09:12.817 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 19:09:12.818 [Information] BackupService: Compressing backup data
2025-07-05 19:09:12.819 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (448 bytes)
2025-07-05 19:09:12.819 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 19:09:12.819 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 19:09:12.820 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 19:09:12.820 [Information] BackupService: Compressing backup data
2025-07-05 19:09:12.821 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-05 19:09:12.821 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 19:09:12.821 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 19:09:12.823 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 19:09:12.837 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 19:09:12.839 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 19:09:12.898 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 19:09:12.899 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 19:09:12.901 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 19:09:12.901 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 19:09:12.901 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 19:09:12.903 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 19:09:12.904 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 19:09:12.908 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 19:09:12.908 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 19:09:12.917 [Information] LicensingService: Initializing licensing service
2025-07-05 19:09:12.962 [Information] LicensingService: License information loaded successfully
2025-07-05 19:09:12.965 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 19:09:12.965 [Information] App: Licensing service initialized successfully
2025-07-05 19:09:12.965 [Information] App: License status: Trial
2025-07-05 19:09:12.965 [Information] App: Trial period: 30 days remaining
2025-07-05 19:09:12.966 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 19:09:13.135 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-05 19:09:13.136 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-05 19:09:13.137 [Error] VocomArchitectureBridge: Exception during bridge initialization: All pipe instances are busy.
2025-07-05 19:09:13.138 [Warning] BridgedVocomService: Bridge initialization failed - service will operate in limited mode
2025-07-05 19:09:13.190 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 19:09:13.191 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 19:09:13.192 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 19:09:13.192 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 19:09:13.193 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 19:09:13.197 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 19:09:13.198 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 19:09:13.201 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 19:09:13.202 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 19:09:13.203 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 19:09:13.204 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:13.204 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 19:09:13.205 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 19:09:13.205 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:13.206 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 19:09:13.207 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 19:09:13.207 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:13.208 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 19:09:13.209 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 19:09:13.209 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:13.210 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 19:09:13.211 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 19:09:13.211 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-05 19:09:13.212 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 19:09:13.213 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 19:09:13.214 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:13.214 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 19:09:13.215 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 19:09:13.216 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:13.216 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 19:09:13.218 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 19:09:13.219 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:13.220 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 19:09:13.220 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 19:09:13.221 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 19:09:13.222 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 19:09:13.222 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-05 19:09:13.223 [Information] ECUCommunicationService: Attempting to reconnect to the Vocom adapter
2025-07-05 19:09:13.224 [Information] BridgedVocomService: Disconnecting from Vocom device through bridge
2025-07-05 19:09:13.224 [Information] ECUCommunicationService: Vocom device disconnected: 
2025-07-05 19:09:13.225 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 19:09:13.226 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 19:09:13.226 [Information] ECUCommunicationService: Vocom device disconnected: 
2025-07-05 19:09:13.227 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 19:09:13.227 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 19:09:13.229 [Information] MainViewModel: Vocom device  disconnected
2025-07-05 19:09:13.229 [Information] ECUCommunicationService: Vocom device disconnected: 
2025-07-05 19:09:13.230 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-05 19:09:13.231 [Information] ECUCommunicationService: No ECUs are connected
2025-07-05 19:09:13.231 [Information] BridgedVocomService: Successfully disconnected from Vocom device
2025-07-05 19:09:13.232 [Error] BridgedVocomService: Device parameter is null
2025-07-05 19:09:13.232 [Error] ECUCommunicationService: Failed to reconnect to the Vocom adapter
2025-07-05 19:09:13.234 [Error] MainViewModel: ECU error: Failed to reconnect to the Vocom adapter
2025-07-05 19:09:13.235 [Error] MainViewModel: Failed to initialize ECU communication service
