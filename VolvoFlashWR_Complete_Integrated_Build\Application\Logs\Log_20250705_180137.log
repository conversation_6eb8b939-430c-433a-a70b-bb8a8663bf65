Log started at 7/5/2025 6:01:37 PM
2025-07-05 18:01:37.037 [Information] LoggingService: Logging service initialized
2025-07-05 18:01:37.052 [Information] App: Starting integrated application initialization
2025-07-05 18:01:37.053 [Information] DependencyManager: Dependency manager initialized for x86 architecture
2025-07-05 18:01:37.055 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 18:01:37.067 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 18:01:37.068 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 18:01:37.069 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 18:01:37.071 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 18:01:37.081 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 18:01:37.095 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 18:01:37.101 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:01:37.107 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:01:37.115 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:01:37.122 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 18:01:37.130 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 18:01:37.130 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 18:01:37.133 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 18:01:37.136 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 18:01:37.136 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 18:01:37.139 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 18:01:37.140 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:01:37.140 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:01:37.141 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:01:37.152 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 18:01:37.152 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 18:01:37.153 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 18:01:37.164 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 18:01:37.164 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 18:01:37.166 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 18:01:37.168 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 18:01:37.177 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 18:01:37.183 [Information] LibraryExtractor: Copying system libraries
2025-07-05 18:01:37.198 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 18:01:37.206 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 18:01:59.863 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-05 18:02:41.150 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-05 18:03:23.642 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-05 18:04:05.448 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:04:47.447 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:05:41.415 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:06:23.727 [Information] LibraryExtractor: Verifying library extraction
2025-07-05 18:06:23.727 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-05 18:06:23.728 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-05 18:06:23.728 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-05 18:06:23.729 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-05 18:06:23.729 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-05 18:06:23.740 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-05 18:06:23.743 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-05 18:06:23.744 [Information] DependencyManager: Initializing dependency manager
2025-07-05 18:06:23.746 [Information] DependencyManager: Setting up library search paths
2025-07-05 18:06:23.747 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:06:23.748 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 18:06:23.748 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 18:06:23.748 [Information] DependencyManager: Updated PATH environment variable
2025-07-05 18:06:23.753 [Information] DependencyManager: Verifying required directories
2025-07-05 18:06:23.753 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:06:23.754 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 18:06:23.754 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-05 18:06:23.755 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 18:06:23.757 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-05 18:06:23.775 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 18:06:23.779 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 18:06:23.786 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-05 18:06:23.789 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 18:06:23.790 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 18:06:23.791 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:06:23.792 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:06:23.793 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:06:23.796 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-05 18:06:23.797 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-05 18:06:23.798 [Warning] DependencyManager: Failed to load Critical library WUDFPuma.dll: Error 193
2025-07-05 18:06:23.800 [Information] DependencyManager: ✓ Loaded Critical library: apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll (x86)
2025-07-05 18:06:23.804 [Information] DependencyManager: ✓ Loaded Critical library: apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll (x86)
2025-07-05 18:06:23.806 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlus.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll (x86)
2025-07-05 18:06:23.807 [Information] DependencyManager: ✓ Loaded Critical library: Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll (x86)
2025-07-05 18:06:23.809 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll (x86)
2025-07-05 18:06:23.810 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\SysWOW64\msvcr120.dll (x86)
2025-07-05 18:06:23.811 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\SysWOW64\msvcp120.dll (x86)
2025-07-05 18:06:23.812 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-05 18:06:23.813 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\SysWOW64\msvcp140.dll (x86)
2025-07-05 18:06:23.814 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\SysWOW64\vcruntime140.dll (x86)
2025-07-05 18:06:23.815 [Information] DependencyManager: Setting up environment variables
2025-07-05 18:06:23.815 [Information] DependencyManager: Environment variables configured
2025-07-05 18:06:23.821 [Information] DependencyManager: Verifying library loading status
2025-07-05 18:06:24.254 [Information] DependencyManager: Library loading verification: 9/11 (81.8%) critical libraries loaded
2025-07-05 18:06:24.255 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-05 18:06:24.261 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-05 18:06:24.263 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-05 18:06:24.267 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-05 18:06:24.273 [Information] IntegratedStartupService: Verifying system readiness
2025-07-05 18:06:24.273 [Information] IntegratedStartupService: System readiness verification passed
2025-07-05 18:06:24.273 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-05 18:06:24.279 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-05 18:06:24.279 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-05 18:06:24.279 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:06:24.280 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-05 18:06:24.280 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-05 18:06:24.280 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-05 18:06:24.281 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-05 18:06:24.281 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-05 18:06:24.281 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-05 18:06:24.281 [Information] App: Integrated startup completed successfully
2025-07-05 18:06:24.285 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-05 18:06:24.320 [Information] App: Initializing application services
2025-07-05 18:06:24.321 [Information] AppConfigurationService: Initializing configuration service
2025-07-05 18:06:24.322 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-05 18:06:24.380 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 18:06:24.381 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-05 18:06:24.382 [Information] App: Configuration service initialized successfully
2025-07-05 18:06:24.383 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-05 18:06:24.384 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-05 18:06:24.389 [Information] App: Environment variable exists: True, not 'false': False
2025-07-05 18:06:24.389 [Information] App: Final useDummyImplementations value: False
2025-07-05 18:06:24.389 [Information] App: Updating config to NOT use dummy implementations
2025-07-05 18:06:24.405 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-05 18:06:24.406 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-05 18:06:24.407 [Information] App: usePatchedImplementation flag is: True
2025-07-05 18:06:24.407 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-05 18:06:24.407 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-05 18:06:24.408 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-05 18:06:24.408 [Information] App: verboseLogging flag is: True
2025-07-05 18:06:24.413 [Information] App: Verifying real hardware requirements...
2025-07-05 18:06:24.414 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-05 18:06:24.414 [Information] App: ✓ Found critical library: apci.dll
2025-07-05 18:06:24.414 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-05 18:06:24.415 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-05 18:06:24.415 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-05 18:06:24.415 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-05 18:06:24.416 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-05 18:06:24.416 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-05 18:06:24.428 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-05 18:06:24.432 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-05 18:06:24.435 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-05 18:06:24.440 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-05 18:06:24.441 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-05 18:06:24.441 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-05 18:06:24.442 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-05 18:06:24.442 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-05 18:06:24.442 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-05 18:06:24.443 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-05 18:06:24.444 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-05 18:06:24.445 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-05 18:06:24.446 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-05 18:06:24.446 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x86
2025-07-05 18:06:24.451 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: apci.dll
2025-07-05 18:06:24.452 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: Volvo.ApciPlus.dll
2025-07-05 18:06:24.452 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: WUDFPuma.dll
2025-07-05 18:06:24.452 [Warning] ArchitectureAwareVocomServiceFactory: Found 1 incompatible libraries
2025-07-05 18:06:24.454 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-05 18:06:24.455 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-05 18:06:24.455 [Warning] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - bridge not yet implemented, falling back to dummy mode
2025-07-05 18:06:24.457 [Information] App: Architecture-aware Vocom service created successfully
2025-07-05 18:06:24.459 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 18:06:24.459 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 18:06:24.460 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-05 18:06:24.460 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-05 18:06:24.460 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-05 18:06:24.528 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 18:06:24.628 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 18:06:24.629 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-05 18:06:24.630 [Information] DummyVocomService: Connecting to Vocom device Dummy Vocom Device (dummy)
2025-07-05 18:06:24.831 [Information] DummyVocomService: Connected to Vocom device Dummy Vocom Device (dummy)
2025-07-05 18:06:24.831 [Information] App: Connected to Vocom device Dummy Vocom Device
2025-07-05 18:06:24.849 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-05 18:06:24.854 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:24.854 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-05 18:06:24.859 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 18:06:24.862 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 18:06:24.863 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 18:06:24.871 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 18:06:24.881 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 18:06:24.884 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 18:06:24.901 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 18:06:24.906 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 18:06:24.926 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:06:24.929 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:06:24.941 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:06:24.942 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 18:06:24.943 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 18:06:24.943 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:06:24.943 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:06:24.943 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 18:06:24.944 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:06:24.944 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 18:06:24.945 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 18:06:24.946 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 18:06:24.947 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 18:06:24.947 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 18:06:24.947 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 18:06:24.947 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 18:06:24.948 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 18:06:24.948 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 18:06:24.948 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 18:06:24.954 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 18:06:24.961 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 18:06:24.962 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 18:06:24.978 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 18:06:24.980 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:24.987 [Information] CANRegisterAccess: Read value 0x83 from register 0x0141 (simulated)
2025-07-05 18:06:24.988 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 18:06:24.989 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 18:06:24.989 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 18:06:24.995 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 18:06:24.996 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 18:06:25.001 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 18:06:25.002 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 18:06:25.002 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 18:06:25.008 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 18:06:25.009 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 18:06:25.009 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 18:06:25.015 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 18:06:25.016 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 18:06:25.021 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 18:06:25.022 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 18:06:25.027 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 18:06:25.028 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 18:06:25.033 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 18:06:25.034 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 18:06:25.039 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 18:06:25.040 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 18:06:25.045 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 18:06:25.046 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 18:06:25.051 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 18:06:25.052 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 18:06:25.057 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 18:06:25.058 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 18:06:25.063 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 18:06:25.064 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 18:06:25.069 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 18:06:25.070 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 18:06:25.075 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 18:06:25.076 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 18:06:25.081 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 18:06:25.082 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 18:06:25.087 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 18:06:25.088 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 18:06:25.093 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 18:06:25.094 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 18:06:25.099 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 18:06:25.100 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 18:06:25.105 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 18:06:25.106 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 18:06:25.111 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 18:06:25.112 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 18:06:25.112 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 18:06:25.118 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 18:06:25.119 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 18:06:25.119 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 18:06:25.119 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:25.124 [Information] CANRegisterAccess: Read value 0x2E from register 0x0141 (simulated)
2025-07-05 18:06:25.125 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 18:06:25.125 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 18:06:25.125 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 18:06:25.126 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:06:25.131 [Information] CANRegisterAccess: Read value 0xCA from register 0x0140 (simulated)
2025-07-05 18:06:25.137 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:06:25.142 [Information] CANRegisterAccess: Read value 0x05 from register 0x0140 (simulated)
2025-07-05 18:06:25.149 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:06:25.155 [Information] CANRegisterAccess: Read value 0x12 from register 0x0140 (simulated)
2025-07-05 18:06:25.156 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 18:06:25.156 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:06:25.159 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:06:25.160 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:06:25.170 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 18:06:25.171 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 18:06:25.172 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 18:06:25.175 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 18:06:25.326 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 18:06:25.329 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 18:06:25.329 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 18:06:25.331 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:06:25.331 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:06:25.342 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:06:25.344 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 18:06:25.344 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 18:06:25.355 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 18:06:25.366 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 18:06:25.377 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 18:06:25.388 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 18:06:25.399 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:06:25.402 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:06:25.402 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:06:25.413 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:06:25.414 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 18:06:25.415 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 18:06:25.425 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 18:06:25.436 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 18:06:25.447 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 18:06:25.458 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 18:06:25.469 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 18:06:25.480 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:06:25.489 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:06:25.489 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:06:25.500 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:06:25.502 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 18:06:25.502 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:06:25.502 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:06:25.503 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 18:06:25.503 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:06:25.503 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 18:06:25.504 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 18:06:25.504 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 18:06:25.504 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 18:06:25.504 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 18:06:25.505 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 18:06:25.505 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 18:06:25.505 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 18:06:25.506 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 18:06:25.506 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 18:06:25.506 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 18:06:25.606 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:06:25.607 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 18:06:25.611 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 18:06:25.613 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:25.613 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 18:06:25.614 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 18:06:25.614 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:25.614 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 18:06:25.615 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 18:06:25.615 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:25.616 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 18:06:25.616 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 18:06:25.616 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:25.617 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 18:06:25.617 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 18:06:25.618 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-05 18:06:25.635 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-05 18:06:25.637 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-05 18:06:25.640 [Information] BackupService: Initializing backup service
2025-07-05 18:06:25.643 [Information] BackupService: Backup service initialized successfully
2025-07-05 18:06:25.643 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-05 18:06:25.644 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-05 18:06:25.646 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-05 18:06:25.685 [Information] BackupService: Compressing backup data
2025-07-05 18:06:25.694 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-05 18:06:25.695 [Information] BackupServiceFactory: Created template for category: Production
2025-07-05 18:06:25.696 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-05 18:06:25.696 [Information] BackupService: Compressing backup data
2025-07-05 18:06:25.698 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-05 18:06:25.698 [Information] BackupServiceFactory: Created template for category: Development
2025-07-05 18:06:25.698 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-05 18:06:25.699 [Information] BackupService: Compressing backup data
2025-07-05 18:06:25.700 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-07-05 18:06:25.700 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-05 18:06:25.700 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-05 18:06:25.701 [Information] BackupService: Compressing backup data
2025-07-05 18:06:25.702 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-07-05 18:06:25.702 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-05 18:06:25.702 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-05 18:06:25.703 [Information] BackupService: Compressing backup data
2025-07-05 18:06:25.704 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-05 18:06:25.705 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-05 18:06:25.705 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-05 18:06:25.706 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-05 18:06:25.706 [Information] BackupService: Compressing backup data
2025-07-05 18:06:25.707 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-05 18:06:25.708 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-05 18:06:25.709 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-05 18:06:25.712 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-05 18:06:25.715 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 18:06:25.729 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 18:06:25.811 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 18:06:25.812 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 18:06:25.814 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-05 18:06:25.814 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-05 18:06:25.815 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-05 18:06:25.816 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-05 18:06:25.817 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-05 18:06:25.824 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-05 18:06:25.824 [Information] App: Flash operation monitor service initialized successfully
2025-07-05 18:06:25.839 [Information] LicensingService: Initializing licensing service
2025-07-05 18:06:25.891 [Information] LicensingService: License information loaded successfully
2025-07-05 18:06:25.894 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-05 18:06:25.894 [Information] App: Licensing service initialized successfully
2025-07-05 18:06:25.895 [Information] App: License status: Trial
2025-07-05 18:06:25.895 [Information] App: Trial period: 30 days remaining
2025-07-05 18:06:25.896 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-05 18:06:26.076 [Information] DummyVocomService: Initializing dummy Vocom service
2025-07-05 18:06:26.076 [Information] DummyVocomService: Dummy Vocom service initialized successfully
2025-07-05 18:06:26.127 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-05 18:06:26.128 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-05 18:06:26.128 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-05 18:06:26.128 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-05 18:06:26.129 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-05 18:06:26.130 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-05 18:06:26.131 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-05 18:06:26.132 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-05 18:06:26.132 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:06:26.133 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-05 18:06:26.144 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:06:26.144 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-05 18:06:26.144 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-05 18:06:26.145 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:06:26.145 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:06:26.145 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-05 18:06:26.145 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:06:26.146 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-05 18:06:26.146 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-05 18:06:26.146 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-05 18:06:26.147 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-05 18:06:26.147 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-05 18:06:26.147 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-05 18:06:26.147 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-05 18:06:26.148 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-05 18:06:26.148 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-05 18:06:26.148 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-05 18:06:26.148 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-05 18:06:26.155 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-05 18:06:26.155 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-05 18:06:26.155 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-05 18:06:26.156 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:26.161 [Information] CANRegisterAccess: Read value 0xA8 from register 0x0141 (simulated)
2025-07-05 18:06:26.167 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:26.173 [Information] CANRegisterAccess: Read value 0xBB from register 0x0141 (simulated)
2025-07-05 18:06:26.174 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-05 18:06:26.174 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-05 18:06:26.175 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-05 18:06:26.180 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-05 18:06:26.181 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-05 18:06:26.187 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-05 18:06:26.188 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-05 18:06:26.188 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-05 18:06:26.194 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-05 18:06:26.195 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-05 18:06:26.195 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-05 18:06:26.201 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-05 18:06:26.202 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-05 18:06:26.208 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-05 18:06:26.209 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-05 18:06:26.215 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-05 18:06:26.216 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-05 18:06:26.222 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-05 18:06:26.223 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-05 18:06:26.229 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-05 18:06:26.230 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-05 18:06:26.236 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-05 18:06:26.237 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-05 18:06:26.242 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-05 18:06:26.243 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-05 18:06:26.249 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-05 18:06:26.250 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-05 18:06:26.256 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-05 18:06:26.257 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-05 18:06:26.263 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-05 18:06:26.264 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-05 18:06:26.271 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-05 18:06:26.271 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-05 18:06:26.278 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-05 18:06:26.278 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-05 18:06:26.284 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-05 18:06:26.285 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-05 18:06:26.291 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-05 18:06:26.292 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-05 18:06:26.298 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-05 18:06:26.299 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-05 18:06:26.306 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-05 18:06:26.306 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-05 18:06:26.312 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-05 18:06:26.313 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-05 18:06:26.313 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-05 18:06:26.319 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-05 18:06:26.320 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-05 18:06:26.320 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-05 18:06:26.321 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:26.326 [Information] CANRegisterAccess: Read value 0x61 from register 0x0141 (simulated)
2025-07-05 18:06:26.332 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:26.338 [Information] CANRegisterAccess: Read value 0x07 from register 0x0141 (simulated)
2025-07-05 18:06:26.344 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:26.350 [Information] CANRegisterAccess: Read value 0x29 from register 0x0141 (simulated)
2025-07-05 18:06:26.356 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-05 18:06:26.362 [Information] CANRegisterAccess: Read value 0xBC from register 0x0141 (simulated)
2025-07-05 18:06:26.363 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-05 18:06:26.363 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-05 18:06:26.363 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-05 18:06:26.364 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:06:26.369 [Information] CANRegisterAccess: Read value 0xEC from register 0x0140 (simulated)
2025-07-05 18:06:26.375 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-05 18:06:26.381 [Information] CANRegisterAccess: Read value 0xD4 from register 0x0140 (simulated)
2025-07-05 18:06:26.382 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-05 18:06:26.382 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-05 18:06:26.383 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:06:26.383 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-05 18:06:26.394 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-05 18:06:26.395 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-05 18:06:26.395 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-05 18:06:26.395 [Information] DummyVocomService: Sending data and waiting for response (dummy)
2025-07-05 18:06:26.546 [Information] DummyVocomService: Sent 4 bytes and received 6 bytes response (dummy)
2025-07-05 18:06:26.546 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-05 18:06:26.547 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-05 18:06:26.547 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:06:26.548 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-05 18:06:26.559 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:06:26.559 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-05 18:06:26.560 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-05 18:06:26.571 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-05 18:06:26.582 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-05 18:06:26.593 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-05 18:06:26.604 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-05 18:06:26.615 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-05 18:06:26.615 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:06:26.615 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-05 18:06:26.627 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:06:26.627 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-05 18:06:26.628 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-05 18:06:26.639 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-05 18:06:26.650 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-05 18:06:26.661 [Information] IICProtocolHandler: Enabling IIC module
2025-07-05 18:06:26.672 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-05 18:06:26.683 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-05 18:06:26.693 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-05 18:06:26.694 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:06:26.694 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-05 18:06:26.705 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:06:26.705 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-05 18:06:26.706 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-05 18:06:26.706 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-05 18:06:26.706 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-05 18:06:26.706 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-05 18:06:26.707 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-05 18:06:26.707 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-05 18:06:26.707 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-05 18:06:26.707 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-05 18:06:26.708 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-05 18:06:26.708 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-05 18:06:26.708 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-05 18:06:26.709 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-05 18:06:26.709 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-05 18:06:26.709 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-05 18:06:26.709 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-05 18:06:26.810 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-05 18:06:26.811 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-05 18:06:26.811 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-05 18:06:26.811 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:26.812 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-05 18:06:26.812 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-05 18:06:26.812 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:26.813 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-05 18:06:26.813 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-05 18:06:26.813 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:26.814 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-05 18:06:26.814 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-05 18:06:26.814 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-05 18:06:26.815 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-05 18:06:26.815 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-05 18:06:26.865 [Information] BackupService: Initializing backup service
2025-07-05 18:06:26.865 [Information] BackupService: Backup service initialized successfully
2025-07-05 18:06:26.917 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-05 18:06:26.917 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-05 18:06:26.919 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-05 18:06:26.919 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-05 18:06:26.970 [Information] BackupService: Getting predefined backup categories
2025-07-05 18:06:27.021 [Information] MainViewModel: Services initialized successfully
2025-07-05 18:06:27.029 [Information] MainViewModel: Scanning for Vocom devices
2025-07-05 18:06:27.030 [Information] DummyVocomService: Scanning for Vocom devices (dummy)
2025-07-05 18:06:27.144 [Information] DummyVocomService: Found 1 Vocom devices (dummy)
2025-07-05 18:06:27.146 [Information] MainViewModel: Found 1 Vocom device(s)
