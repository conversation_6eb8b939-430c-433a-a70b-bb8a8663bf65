# Bundle Visual C++ Redistributable Libraries Script
# This script downloads and bundles the required VC++ runtime libraries
# to avoid the 5-7 minute download time on first startup

param(
    [string]$OutputPath = "Libraries\VCRedist",
    [switch]$Force
)

Write-Host "=== Visual C++ Redistributable Libraries Bundler ===" -ForegroundColor Cyan
Write-Host "This script will download and bundle VC++ runtime libraries" -ForegroundColor Yellow
Write-Host ""

# Create output directory
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created directory: $OutputPath" -ForegroundColor Green
}

# Define required libraries with download URLs
$libraries = @{
    "msvcr120.dll" = @{
        "Description" = "Microsoft Visual C++ 2013 Runtime (x64)"
        "Url" = "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe"
        "Required" = $true
    }
    "msvcp120.dll" = @{
        "Description" = "Microsoft Visual C++ 2013 Runtime (x64)"
        "Url" = "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe"
        "Required" = $true
    }
    "msvcr140.dll" = @{
        "Description" = "Microsoft Visual C++ 2015-2022 Runtime (x64)"
        "Url" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        "Required" = $true
    }
    "msvcp140.dll" = @{
        "Description" = "Microsoft Visual C++ 2015-2022 Runtime (x64)"
        "Url" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        "Required" = $true
    }
    "vcruntime140.dll" = @{
        "Description" = "Microsoft Visual C++ 2015-2022 Runtime (x64)"
        "Url" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        "Required" = $true
    }
    "api-ms-win-crt-runtime-l1-1-0.dll" = @{
        "Description" = "Universal CRT Runtime"
        "Url" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        "Required" = $true
    }
    "api-ms-win-crt-heap-l1-1-0.dll" = @{
        "Description" = "Universal CRT Heap"
        "Url" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        "Required" = $true
    }
    "api-ms-win-crt-string-l1-1-0.dll" = @{
        "Description" = "Universal CRT String"
        "Url" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        "Required" = $true
    }
}

# Function to check if library exists in system
function Test-SystemLibrary {
    param([string]$LibraryName)
    
    $systemPaths = @(
        "$env:SystemRoot\System32",
        "$env:SystemRoot\SysWOW64",
        "$env:ProgramFiles\Microsoft Visual Studio\2022\Community\VC\Redist\MSVC\*\x64\Microsoft.VC*.CRT",
        "$env:ProgramFiles(x86)\Microsoft Visual Studio\2019\*\VC\Redist\MSVC\*\x64\Microsoft.VC*.CRT"
    )
    
    foreach ($path in $systemPaths) {
        $expandedPaths = Get-ChildItem -Path $path -ErrorAction SilentlyContinue
        foreach ($expandedPath in $expandedPaths) {
            $libraryPath = Join-Path $expandedPath.FullName $LibraryName
            if (Test-Path $libraryPath) {
                return $libraryPath
            }
        }
    }
    
    return $null
}

# Function to copy system library
function Copy-SystemLibrary {
    param(
        [string]$LibraryName,
        [string]$DestinationPath
    )
    
    $systemPath = Test-SystemLibrary -LibraryName $LibraryName
    if ($systemPath) {
        try {
            Copy-Item -Path $systemPath -Destination $DestinationPath -Force
            Write-Host "✓ Copied $LibraryName from system" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "✗ Failed to copy $LibraryName from system: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    
    return $false
}

# Check and bundle libraries
$bundledCount = 0
$totalCount = $libraries.Count

Write-Host "Checking and bundling $totalCount Visual C++ runtime libraries..." -ForegroundColor Cyan
Write-Host ""

foreach ($library in $libraries.GetEnumerator()) {
    $libraryName = $library.Key
    $libraryInfo = $library.Value
    $destinationPath = Join-Path $OutputPath $libraryName
    
    Write-Host "Processing: $libraryName" -ForegroundColor Yellow
    Write-Host "  Description: $($libraryInfo.Description)" -ForegroundColor Gray
    
    # Check if library already exists in output
    if ((Test-Path $destinationPath) -and !$Force) {
        Write-Host "  ✓ Already bundled" -ForegroundColor Green
        $bundledCount++
        continue
    }
    
    # Try to copy from system first
    if (Copy-SystemLibrary -LibraryName $libraryName -DestinationPath $destinationPath) {
        $bundledCount++
        continue
    }
    
    # If not found in system, provide guidance
    Write-Host "  ⚠ Not found in system" -ForegroundColor Yellow
    Write-Host "  📥 Download URL: $($libraryInfo.Url)" -ForegroundColor Cyan
    Write-Host "  💡 Install the Visual C++ Redistributable package to make this library available" -ForegroundColor Gray
    
    if ($libraryInfo.Required) {
        Write-Host "  ❗ This library is required for proper operation" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host ""
Write-Host "=== Bundling Summary ===" -ForegroundColor Cyan
Write-Host "Libraries bundled: $bundledCount / $totalCount" -ForegroundColor $(if ($bundledCount -eq $totalCount) { "Green" } else { "Yellow" })

if ($bundledCount -lt $totalCount) {
    Write-Host ""
    Write-Host "⚠ Some libraries could not be bundled automatically" -ForegroundColor Yellow
    Write-Host "To resolve this:" -ForegroundColor White
    Write-Host "1. Install Visual C++ 2015-2022 Redistributable (x64)" -ForegroundColor White
    Write-Host "   Download: https://aka.ms/vs/17/release/vc_redist.x64.exe" -ForegroundColor Cyan
    Write-Host "2. Install Visual C++ 2013 Redistributable (x64)" -ForegroundColor White
    Write-Host "   Download: https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe" -ForegroundColor Cyan
    Write-Host "3. Run this script again with -Force parameter" -ForegroundColor White
    Write-Host ""
    Write-Host "Alternative: The application will download missing libraries on first run (5-7 minutes)" -ForegroundColor Gray
} else {
    Write-Host ""
    Write-Host "✅ All Visual C++ runtime libraries have been bundled successfully!" -ForegroundColor Green
    Write-Host "The application will start faster without needing to download libraries" -ForegroundColor Green
}

Write-Host ""
Write-Host "Bundled libraries location: $OutputPath" -ForegroundColor Cyan
Write-Host "=== Bundling Complete ===" -ForegroundColor Cyan
