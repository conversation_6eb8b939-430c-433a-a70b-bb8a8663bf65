Log started at 7/6/2025 2:07:04 PM
2025-07-06 14:07:04.759 [Information] LoggingService: Logging service initialized
2025-07-06 14:07:04.780 [Information] App: Starting integrated application initialization
2025-07-06 14:07:04.782 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 14:07:04.786 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 14:07:04.788 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 14:07:04.789 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 14:07:04.792 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 14:07:04.795 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 14:07:04.801 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 14:07:04.818 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:07:04.826 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.830 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.831 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 14:07:04.837 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:07:04.841 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.844 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.845 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:07:04.851 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:07:04.854 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.858 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.859 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:07:04.863 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:07:04.866 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.870 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:07:04.871 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:07:04.874 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 14:07:04.877 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 14:07:04.877 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 14:07:04.898 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 14:07:04.899 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 14:07:04.909 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 14:07:04.910 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 14:07:04.910 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 14:07:05.014 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 14:07:05.014 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 14:07:05.100 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 14:07:05.101 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:07:05.101 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:07:05.102 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:07:05.112 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 14:07:05.113 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 14:07:05.113 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 14:07:05.122 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 14:07:05.123 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 14:07:05.125 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 14:07:05.128 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 14:07:05.133 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 14:07:05.136 [Information] LibraryExtractor: Copying system libraries
2025-07-06 14:07:05.144 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 14:07:05.159 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 14:07:29.882 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 14:08:20.611 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 14:09:06.956 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 14:10:05.695 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:11:18.126 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:12:02.798 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:12:48.396 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 14:12:48.399 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 14:12:48.399 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 14:12:48.401 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 14:12:48.402 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 14:12:48.404 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 14:12:48.411 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 14:12:48.415 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 14:12:48.417 [Information] DependencyManager: Initializing dependency manager
2025-07-06 14:12:48.420 [Information] DependencyManager: Setting up library search paths
2025-07-06 14:12:48.422 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:12:48.422 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 14:12:48.423 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 14:12:48.423 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 14:12:48.426 [Information] DependencyManager: Verifying required directories
2025-07-06 14:12:48.426 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:12:48.427 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 14:12:48.427 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 14:12:48.428 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 14:12:48.430 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 14:12:48.447 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 14:12:48.458 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 14:12:48.462 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 14:12:48.472 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 14:12:48.479 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 14:12:48.481 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:12:48.483 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:12:48.487 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:12:48.489 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 14:12:48.592 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 14:12:48.959 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 14:12:48.960 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 14:12:49.203 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 14:12:49.204 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 14:12:49.207 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 14:12:49.404 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 14:12:49.406 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 14:12:49.549 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 14:12:49.550 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 14:12:49.551 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 14:12:50.242 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 14:12:50.246 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 14:12:51.156 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 14:12:51.157 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 14:12:51.160 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 14:12:51.334 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 14:12:51.335 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 14:12:51.635 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 14:12:51.645 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 14:12:51.651 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 14:12:51.892 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 14:12:51.893 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 14:12:51.895 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 14:12:51.896 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 14:12:51.910 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 14:12:51.912 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 14:12:51.913 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 14:12:51.914 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 14:12:51.915 [Information] DependencyManager: Setting up environment variables
2025-07-06 14:12:51.915 [Information] DependencyManager: Environment variables configured
2025-07-06 14:12:51.918 [Information] DependencyManager: Verifying library loading status
2025-07-06 14:12:52.612 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 14:12:52.612 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 14:12:52.613 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 14:12:52.616 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 14:12:52.619 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 14:12:52.627 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 14:12:52.630 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 14:12:52.631 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 14:12:52.631 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 14:12:52.633 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 14:12:52.634 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 14:12:52.634 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:12:52.634 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 14:12:52.635 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 14:12:52.635 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 14:12:52.636 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 14:12:52.636 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:12:52.637 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 14:12:52.637 [Information] App: Integrated startup completed successfully
2025-07-06 14:12:52.643 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 14:12:52.673 [Information] App: Initializing application services
2025-07-06 14:12:52.676 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 14:12:52.677 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 14:12:52.760 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 14:12:52.761 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 14:12:52.762 [Information] App: Configuration service initialized successfully
2025-07-06 14:12:52.764 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 14:12:52.765 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 14:12:52.773 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 14:12:52.774 [Information] App: Final useDummyImplementations value: False
2025-07-06 14:12:52.774 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 14:12:52.776 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 14:12:52.803 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 14:12:52.805 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 14:12:52.805 [Information] App: usePatchedImplementation flag is: True
2025-07-06 14:12:52.806 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 14:12:52.806 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 14:12:52.807 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 14:12:52.807 [Information] App: verboseLogging flag is: True
2025-07-06 14:12:52.810 [Information] App: Verifying real hardware requirements...
2025-07-06 14:12:52.811 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 14:12:52.812 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 14:12:52.812 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 14:12:52.813 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 14:12:52.814 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 14:12:52.815 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 14:12:52.815 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 14:12:52.816 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 14:12:52.827 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 14:12:52.830 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 14:12:52.834 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 14:12:52.839 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 14:12:52.839 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 14:12:52.840 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 14:12:52.840 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 14:12:52.841 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 14:12:52.842 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 14:12:52.842 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 14:12:52.843 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 14:12:52.843 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 14:12:52.846 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 14:12:52.846 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 14:12:52.849 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 14:12:52.850 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 14:12:52.850 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 14:12:52.851 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 14:12:52.853 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 14:12:52.854 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 14:12:52.856 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 14:12:52.860 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 14:12:52.864 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 14:12:52.970 [Information] VocomArchitectureBridge: Started bridge process with PID 10788
2025-07-06 14:12:53.973 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 14:12:55.966 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 14:12:56.163 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 14:12:56.164 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 14:12:56.165 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 14:12:56.165 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 14:12:56.166 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 14:12:56.166 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 14:12:56.166 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 14:12:56.167 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 14:12:56.167 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 14:12:56.167 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 14:12:56.168 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 14:12:56.219 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:12:56.277 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:12:56.279 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:12:56.279 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 14:12:56.284 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 14:12:56.289 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:56.290 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 14:12:56.295 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 14:12:56.298 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 14:12:56.299 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 14:12:56.304 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 14:12:56.309 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 14:12:56.315 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 14:12:56.320 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 14:12:56.327 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 14:12:56.337 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:12:56.342 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:12:56.342 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:56.346 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:12:56.346 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:12:56.347 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:56.350 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:12:56.351 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:12:56.351 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:56.354 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:12:56.356 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:12:56.356 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:56.359 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:12:56.360 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:12:56.360 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:56.361 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 14:12:56.365 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 14:12:56.367 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:56.368 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 14:12:56.368 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 14:12:56.369 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:56.369 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 14:12:56.370 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 14:12:56.370 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:56.371 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 14:12:56.372 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 14:12:56.373 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:56.373 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 14:12:56.374 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 14:12:56.374 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 14:12:56.376 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:12:56.378 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:12:56.378 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:12:56.380 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 14:12:56.380 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 14:12:56.381 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 14:12:57.382 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 14:12:57.383 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 14:12:57.383 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 14:12:57.385 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 14:12:57.385 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 14:12:57.386 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 14:12:57.390 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 14:12:57.390 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 14:12:57.391 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 14:12:57.392 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:12:57.392 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:12:57.395 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:57.395 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:12:57.396 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:12:57.396 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:57.396 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:12:57.397 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:12:57.397 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:57.397 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:12:57.398 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:12:57.398 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:57.398 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:12:57.398 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:12:57.399 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:57.399 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 14:12:57.399 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 14:12:57.400 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:57.400 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 14:12:57.400 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 14:12:57.401 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:57.401 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 14:12:57.401 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 14:12:57.402 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:57.403 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 14:12:57.403 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 14:12:57.404 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:57.404 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 14:12:57.404 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 14:12:57.405 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 14:12:57.405 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:12:57.408 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:12:57.408 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:12:57.409 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 14:12:57.409 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 14:12:57.409 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 14:12:59.409 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 14:12:59.410 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 14:12:59.410 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 14:12:59.410 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 14:12:59.411 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 14:12:59.411 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 14:12:59.412 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 14:12:59.413 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 14:12:59.414 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 14:12:59.414 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:12:59.414 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:12:59.415 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:59.415 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:12:59.415 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:12:59.416 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:59.416 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:12:59.416 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:12:59.417 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:59.417 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:12:59.417 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:12:59.418 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:59.418 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:12:59.418 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:12:59.419 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:12:59.419 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 14:12:59.419 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 14:12:59.420 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:59.420 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 14:12:59.420 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 14:12:59.421 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:59.421 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 14:12:59.422 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 14:12:59.422 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:59.423 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 14:12:59.423 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 14:12:59.426 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:12:59.426 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 14:12:59.427 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 14:12:59.427 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 14:12:59.428 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:12:59.429 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:12:59.430 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:12:59.430 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 14:12:59.430 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 14:12:59.431 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 14:13:02.431 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 14:13:02.431 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 14:13:02.433 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 14:13:02.434 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 14:13:02.935 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 14:13:02.935 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 14:13:02.937 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 14:13:02.937 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 14:13:02.941 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 14:13:02.944 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 14:13:02.947 [Information] BackupService: Initializing backup service
2025-07-06 14:13:02.947 [Information] BackupService: Backup service initialized successfully
2025-07-06 14:13:02.948 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 14:13:02.948 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 14:13:02.952 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 14:13:02.979 [Information] BackupService: Compressing backup data
2025-07-06 14:13:02.988 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-06 14:13:02.993 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 14:13:02.993 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 14:13:02.994 [Information] BackupService: Compressing backup data
2025-07-06 14:13:02.996 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-06 14:13:02.997 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 14:13:02.997 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 14:13:02.998 [Information] BackupService: Compressing backup data
2025-07-06 14:13:02.999 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-06 14:13:03.002 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 14:13:03.002 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 14:13:03.003 [Information] BackupService: Compressing backup data
2025-07-06 14:13:03.005 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-07-06 14:13:03.005 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 14:13:03.005 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 14:13:03.006 [Information] BackupService: Compressing backup data
2025-07-06 14:13:03.009 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-06 14:13:03.010 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 14:13:03.010 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 14:13:03.011 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 14:13:03.011 [Information] BackupService: Compressing backup data
2025-07-06 14:13:03.013 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-07-06 14:13:03.013 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 14:13:03.013 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 14:13:03.016 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 14:13:03.020 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 14:13:03.022 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 14:13:03.097 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 14:13:03.098 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 14:13:03.099 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 14:13:03.100 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 14:13:03.100 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 14:13:03.102 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 14:13:03.103 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 14:13:03.108 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 14:13:03.108 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 14:13:03.118 [Information] LicensingService: Initializing licensing service
2025-07-06 14:13:03.180 [Information] LicensingService: License information loaded successfully
2025-07-06 14:13:03.183 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 14:13:03.184 [Information] App: Licensing service initialized successfully
2025-07-06 14:13:03.184 [Information] App: License status: Trial
2025-07-06 14:13:03.185 [Information] App: Trial period: 29 days remaining
2025-07-06 14:13:03.186 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 14:13:03.379 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 14:13:03.379 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 14:13:03.379 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 14:13:03.380 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 14:13:03.430 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 14:13:03.931 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 14:13:03.982 [Information] BackupService: Initializing backup service
2025-07-06 14:13:03.983 [Information] BackupService: Backup service initialized successfully
2025-07-06 14:13:04.034 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 14:13:04.035 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 14:13:04.036 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 14:13:04.036 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 14:13:04.089 [Information] BackupService: Getting predefined backup categories
2025-07-06 14:13:04.141 [Information] MainViewModel: Services initialized successfully
2025-07-06 14:13:04.144 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 14:13:04.145 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:13:04.162 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:13:04.163 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:13:04.164 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 14:13:30.209 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 14:13:30.211 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:13:30.236 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:13:30.238 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:13:30.240 [Information] MainViewModel: Found 0 Vocom device(s)
