Log started at 7/6/2025 2:43:31 PM
2025-07-06 14:43:31.468 [Information] LoggingService: Logging service initialized
2025-07-06 14:43:31.489 [Information] App: Starting integrated application initialization
2025-07-06 14:43:31.491 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 14:43:31.494 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 14:43:31.496 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 14:43:31.499 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 14:43:31.502 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 14:43:31.505 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 14:43:31.509 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 14:43:31.521 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:43:31.524 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.527 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.528 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 14:43:31.535 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:43:31.539 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.542 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.545 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:43:31.553 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:43:31.560 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.565 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.567 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:43:31.570 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-06 14:43:31.573 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.576 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-06 14:43:31.576 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:43:31.579 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 14:43:31.584 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 14:43:31.584 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 14:43:31.586 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 14:43:31.587 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 14:43:31.588 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 14:43:31.589 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 14:43:31.589 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 14:43:31.590 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 14:43:31.590 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 14:43:31.591 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 14:43:31.592 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:43:31.592 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:43:31.593 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:43:31.601 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 14:43:31.602 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 14:43:31.603 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 14:43:31.609 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 14:43:31.610 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 14:43:31.611 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 14:43:31.614 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 14:43:31.619 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 14:43:31.622 [Information] LibraryExtractor: Copying system libraries
2025-07-06 14:43:31.629 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 14:43:31.640 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 14:44:02.225 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 14:44:55.473 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 14:45:41.381 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 14:46:35.612 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:47:40.087 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:48:28.808 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:49:14.366 [Information] LibraryExtractor: Verifying library extraction
2025-07-06 14:49:14.369 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-06 14:49:14.370 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-06 14:49:14.370 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-06 14:49:14.371 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-06 14:49:14.371 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-06 14:49:14.379 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-06 14:49:14.382 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-06 14:49:14.387 [Information] DependencyManager: Initializing dependency manager
2025-07-06 14:49:14.389 [Information] DependencyManager: Setting up library search paths
2025-07-06 14:49:14.390 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:49:14.392 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 14:49:14.392 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 14:49:14.393 [Information] DependencyManager: Updated PATH environment variable
2025-07-06 14:49:14.395 [Information] DependencyManager: Verifying required directories
2025-07-06 14:49:14.395 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:49:14.396 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 14:49:14.396 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-06 14:49:14.397 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 14:49:14.402 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-06 14:49:14.411 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 14:49:14.413 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 14:49:14.419 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-06 14:49:14.422 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 14:49:14.424 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 14:49:14.425 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 14:49:14.427 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 14:49:14.428 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 14:49:14.430 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-06 14:49:14.433 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-06 14:49:14.435 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 14:49:14.436 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-06 14:49:14.436 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-06 14:49:14.437 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-06 14:49:14.438 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-06 14:49:14.440 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 14:49:14.441 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-06 14:49:14.441 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-06 14:49:14.442 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-06 14:49:14.443 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-06 14:49:14.444 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 14:49:14.445 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-06 14:49:14.445 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-06 14:49:14.446 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-06 14:49:14.448 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-06 14:49:14.450 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 14:49:14.452 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-06 14:49:14.454 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-06 14:49:14.456 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-06 14:49:14.458 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-06 14:49:14.459 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-06 14:49:14.459 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 14:49:14.461 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-06 14:49:14.463 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-06 14:49:14.465 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-06 14:49:14.467 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-06 14:49:14.469 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-06 14:49:14.470 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-06 14:49:14.473 [Information] DependencyManager: Setting up environment variables
2025-07-06 14:49:14.473 [Information] DependencyManager: Environment variables configured
2025-07-06 14:49:14.476 [Information] DependencyManager: Verifying library loading status
2025-07-06 14:49:15.164 [Information] DependencyManager: Library loading verification: 6/11 (54.5%) critical libraries loaded
2025-07-06 14:49:15.168 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-06 14:49:15.169 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-06 14:49:15.178 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-06 14:49:15.181 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-06 14:49:15.192 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-06 14:49:15.197 [Information] IntegratedStartupService: Verifying system readiness
2025-07-06 14:49:15.198 [Information] IntegratedStartupService: System readiness verification passed
2025-07-06 14:49:15.205 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-06 14:49:15.209 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-06 14:49:15.210 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-06 14:49:15.210 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:49:15.210 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-06 14:49:15.211 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-06 14:49:15.211 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-06 14:49:15.212 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-06 14:49:15.212 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-06 14:49:15.213 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-06 14:49:15.213 [Information] App: Integrated startup completed successfully
2025-07-06 14:49:15.219 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-06 14:49:15.240 [Information] App: Initializing application services
2025-07-06 14:49:15.242 [Information] AppConfigurationService: Initializing configuration service
2025-07-06 14:49:15.243 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-06 14:49:15.300 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 14:49:15.302 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-06 14:49:15.303 [Information] App: Configuration service initialized successfully
2025-07-06 14:49:15.306 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-06 14:49:15.306 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-06 14:49:15.316 [Information] App: Environment variable exists: True, not 'false': False
2025-07-06 14:49:15.317 [Information] App: Final useDummyImplementations value: False
2025-07-06 14:49:15.318 [Information] App: Updating config to NOT use dummy implementations
2025-07-06 14:49:15.320 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-06 14:49:15.337 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-06 14:49:15.340 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-06 14:49:15.340 [Information] App: usePatchedImplementation flag is: True
2025-07-06 14:49:15.341 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-06 14:49:15.342 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-06 14:49:15.343 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-06 14:49:15.343 [Information] App: verboseLogging flag is: True
2025-07-06 14:49:15.346 [Information] App: Verifying real hardware requirements...
2025-07-06 14:49:15.346 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-06 14:49:15.347 [Information] App: ✓ Found critical library: apci.dll
2025-07-06 14:49:15.347 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-06 14:49:15.348 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-06 14:49:15.349 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-06 14:49:15.349 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-06 14:49:15.350 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-06 14:49:15.350 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-06 14:49:15.362 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-07-06 14:49:15.365 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-07-06 14:49:15.372 [Warning] RuntimeDependencyResolver: msvcr140.dll not found, attempting to resolve
2025-07-06 14:49:15.376 [Warning] RuntimeDependencyResolver: Could not resolve msvcr140.dll - Visual C++ 2015-2022 Redistributable may need to be installed
2025-07-06 14:49:15.376 [Warning] App: Some runtime dependencies could not be resolved automatically
2025-07-06 14:49:15.377 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-07-06 14:49:15.378 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-07-06 14:49:15.379 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-07-06 14:49:15.380 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-07-06 14:49:15.380 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-07-06 14:49:15.381 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-07-06 14:49:15.382 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-07-06 14:49:15.386 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-07-06 14:49:15.386 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-07-06 14:49:15.389 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-07-06 14:49:15.390 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-07-06 14:49:15.391 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-07-06 14:49:15.392 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-07-06 14:49:15.395 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-07-06 14:49:15.395 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridged Vocom service
2025-07-06 14:49:15.397 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - creating bridged Vocom service
2025-07-06 14:49:15.402 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 14:49:15.407 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 14:49:15.422 [Information] VocomArchitectureBridge: Started bridge process with PID 18104
2025-07-06 14:49:16.425 [Information] VocomArchitectureBridge: Waiting for bridge process to connect...
2025-07-06 14:49:16.432 [Information] VocomArchitectureBridge: Bridge process connected successfully
2025-07-06 14:49:16.570 [Information] VocomArchitectureBridge: Architecture bridge initialized successfully
2025-07-06 14:49:16.571 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 14:49:16.572 [Information] ArchitectureAwareVocomServiceFactory: Bridged Vocom service created and initialized successfully
2025-07-06 14:49:16.572 [Information] App: Architecture-aware Vocom service created successfully
2025-07-06 14:49:16.573 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 14:49:16.573 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 14:49:16.573 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 14:49:16.574 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 14:49:16.574 [Information] App: Architecture-aware Vocom service initialized successfully
2025-07-06 14:49:16.575 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-07-06 14:49:16.575 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-06 14:49:16.642 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:49:16.724 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:49:16.726 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:49:16.727 [Warning] App: No Vocom devices found, continuing without a connected device
2025-07-06 14:49:16.732 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-06 14:49:16.738 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:16.739 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-06 14:49:16.744 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 14:49:16.747 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 14:49:16.747 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 14:49:16.754 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 14:49:16.758 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 14:49:16.763 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 14:49:16.770 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 14:49:16.774 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 14:49:16.789 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:49:16.795 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:49:16.796 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:16.803 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:49:16.804 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:49:16.804 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:16.808 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:49:16.809 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:49:16.809 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:16.813 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:49:16.814 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:49:16.814 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:16.817 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:49:16.819 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:49:16.819 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:16.820 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 14:49:16.826 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 14:49:16.829 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:16.829 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 14:49:16.830 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 14:49:16.831 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:16.831 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 14:49:16.832 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 14:49:16.832 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:16.833 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 14:49:16.834 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 14:49:16.836 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:16.836 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 14:49:16.837 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 14:49:16.837 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 14:49:16.840 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:49:16.844 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:49:16.844 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:49:16.846 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 14:49:16.847 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 14:49:16.848 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-06 14:49:17.849 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-06 14:49:17.849 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 14:49:17.850 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 14:49:17.852 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 14:49:17.852 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 14:49:17.853 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 14:49:17.854 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 14:49:17.854 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 14:49:17.856 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 14:49:17.856 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:49:17.856 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:49:17.857 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:17.857 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:49:17.858 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:49:17.858 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:17.859 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:49:17.859 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:49:17.859 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:17.860 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:49:17.860 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:49:17.860 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:17.860 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:49:17.861 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:49:17.861 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:17.861 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 14:49:17.862 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 14:49:17.862 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:17.862 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 14:49:17.863 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 14:49:17.863 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:17.863 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 14:49:17.864 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 14:49:17.864 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:17.864 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 14:49:17.864 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 14:49:17.865 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:17.865 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 14:49:17.866 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 14:49:17.866 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 14:49:17.866 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:49:17.868 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:49:17.868 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:49:17.869 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 14:49:17.869 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 14:49:17.869 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-06 14:49:19.871 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-06 14:49:19.871 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-06 14:49:19.872 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-06 14:49:19.872 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-06 14:49:19.872 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-06 14:49:19.873 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-06 14:49:19.874 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-06 14:49:19.874 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-06 14:49:19.875 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-06 14:49:19.875 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:49:19.876 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-06 14:49:19.876 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:19.877 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:49:19.877 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-06 14:49:19.877 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:19.878 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:49:19.878 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-06 14:49:19.878 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:19.879 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:49:19.879 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-06 14:49:19.879 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:19.880 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:49:19.880 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-06 14:49:19.880 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-06 14:49:19.880 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-06 14:49:19.881 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-06 14:49:19.881 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:19.882 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-06 14:49:19.882 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-06 14:49:19.882 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:19.883 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-06 14:49:19.883 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-06 14:49:19.883 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:19.884 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-06 14:49:19.884 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-06 14:49:19.885 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-06 14:49:19.885 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-06 14:49:19.885 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-06 14:49:19.887 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-06 14:49:19.888 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:49:19.889 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:49:19.889 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:49:19.890 [Warning] BridgedVocomService: No devices available for connection
2025-07-06 14:49:19.890 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-06 14:49:19.890 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-06 14:49:22.891 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-06 14:49:22.892 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-06 14:49:22.893 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-06 14:49:22.895 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 14:49:23.396 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 14:49:23.397 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-06 14:49:23.398 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-06 14:49:23.398 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-06 14:49:23.402 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-06 14:49:23.404 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-06 14:49:23.407 [Information] BackupService: Initializing backup service
2025-07-06 14:49:23.408 [Information] BackupService: Backup service initialized successfully
2025-07-06 14:49:23.408 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-06 14:49:23.408 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-06 14:49:23.411 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-06 14:49:23.440 [Information] BackupService: Compressing backup data
2025-07-06 14:49:23.449 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-06 14:49:23.451 [Information] BackupServiceFactory: Created template for category: Production
2025-07-06 14:49:23.451 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-06 14:49:23.452 [Information] BackupService: Compressing backup data
2025-07-06 14:49:23.453 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-06 14:49:23.454 [Information] BackupServiceFactory: Created template for category: Development
2025-07-06 14:49:23.454 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-06 14:49:23.455 [Information] BackupService: Compressing backup data
2025-07-06 14:49:23.456 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-07-06 14:49:23.456 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-06 14:49:23.457 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-06 14:49:23.457 [Information] BackupService: Compressing backup data
2025-07-06 14:49:23.458 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-06 14:49:23.459 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-06 14:49:23.459 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-06 14:49:23.459 [Information] BackupService: Compressing backup data
2025-07-06 14:49:23.460 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-07-06 14:49:23.461 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-06 14:49:23.461 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-06 14:49:23.462 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-06 14:49:23.462 [Information] BackupService: Compressing backup data
2025-07-06 14:49:23.463 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (515 bytes)
2025-07-06 14:49:23.464 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-06 14:49:23.464 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-06 14:49:23.466 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-06 14:49:23.471 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 14:49:23.474 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 14:49:23.580 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 14:49:23.581 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 14:49:23.583 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-06 14:49:23.583 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-06 14:49:23.584 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-06 14:49:23.586 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-06 14:49:23.587 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-06 14:49:23.593 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-06 14:49:23.593 [Information] App: Flash operation monitor service initialized successfully
2025-07-06 14:49:23.608 [Information] LicensingService: Initializing licensing service
2025-07-06 14:49:23.674 [Information] LicensingService: License information loaded successfully
2025-07-06 14:49:23.677 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-06 14:49:23.678 [Information] App: Licensing service initialized successfully
2025-07-06 14:49:23.679 [Information] App: License status: Trial
2025-07-06 14:49:23.679 [Information] App: Trial period: 29 days remaining
2025-07-06 14:49:23.680 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-06 14:49:23.895 [Information] BridgedVocomService: Initializing Bridged Vocom Service
2025-07-06 14:49:23.895 [Information] VocomArchitectureBridge: Initializing Vocom Architecture Bridge
2025-07-06 14:49:23.896 [Information] VocomArchitectureBridge: Bridge already initialized
2025-07-06 14:49:23.896 [Information] BridgedVocomService: Bridged Vocom Service initialized successfully
2025-07-06 14:49:23.946 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-06 14:49:24.447 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-06 14:49:24.498 [Information] BackupService: Initializing backup service
2025-07-06 14:49:24.498 [Information] BackupService: Backup service initialized successfully
2025-07-06 14:49:24.550 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-06 14:49:24.550 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-06 14:49:24.552 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-06 14:49:24.553 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-06 14:49:24.606 [Information] BackupService: Getting predefined backup categories
2025-07-06 14:49:24.658 [Information] MainViewModel: Services initialized successfully
2025-07-06 14:49:24.662 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 14:49:24.663 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:49:24.667 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:49:24.667 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:49:24.669 [Information] MainViewModel: Found 0 Vocom device(s)
2025-07-06 14:50:07.672 [Information] MainViewModel: Scanning for Vocom devices
2025-07-06 14:50:07.673 [Information] BridgedVocomService: Scanning for Vocom devices through bridge
2025-07-06 14:50:07.692 [Error] VocomArchitectureBridge: Exception during device detection: The JSON value could not be converted to VolvoFlashWR.Core.Models.VocomConnectionType. Path: $[0].ConnectionType | LineNumber: 0 | BytePositionInLine: 89.
2025-07-06 14:50:07.693 [Information] BridgedVocomService: Found 0 Vocom devices
2025-07-06 14:50:07.694 [Information] MainViewModel: Found 0 Vocom device(s)
