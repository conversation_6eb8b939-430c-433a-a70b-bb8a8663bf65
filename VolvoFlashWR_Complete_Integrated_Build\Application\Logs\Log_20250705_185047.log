Log started at 7/5/2025 6:50:47 PM
2025-07-05 18:50:47.206 [Information] LoggingService: Logging service initialized
2025-07-05 18:50:47.220 [Information] App: Starting integrated application initialization
2025-07-05 18:50:47.222 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-05 18:50:47.224 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-05 18:50:47.225 [Information] IntegratedStartupService: Setting up application environment
2025-07-05 18:50:47.227 [Information] IntegratedStartupService: Application environment setup completed
2025-07-05 18:50:47.230 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-05 18:50:47.232 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-05 18:50:47.236 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-05 18:50:47.245 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 18:50:47.248 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.251 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.252 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-05 18:50:47.255 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 18:50:47.257 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.259 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.260 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:50:47.264 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 18:50:47.267 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.269 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.270 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:50:47.273 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-05 18:50:47.275 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.278 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-05 18:50:47.278 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:50:47.281 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-05 18:50:47.283 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-05 18:50:47.284 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-05 18:50:47.286 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-05 18:50:47.286 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-05 18:50:47.287 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-05 18:50:47.287 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-05 18:50:47.287 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-05 18:50:47.288 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-05 18:50:47.288 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-05 18:50:47.289 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-05 18:50:47.290 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-05 18:50:47.290 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-05 18:50:47.290 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-05 18:50:47.299 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-05 18:50:47.300 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-05 18:50:47.300 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-05 18:50:47.307 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-05 18:50:47.308 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-05 18:50:47.310 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-05 18:50:47.311 [Information] LibraryExtractor: Starting library extraction process
2025-07-05 18:50:47.315 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-05 18:50:47.317 [Information] LibraryExtractor: Copying system libraries
2025-07-05 18:50:47.323 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-05 18:50:47.331 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-05 18:51:10.876 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
