using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;

namespace VolvoFlashWR.Core.Services
{
    /// <summary>
    /// Bundles and manages Visual C++ Redistributable libraries
    /// Ensures all required runtime libraries are available without system installation
    /// </summary>
    public class VCRedistBundler
    {
        private readonly ILoggingService _logger;
        private readonly string _applicationPath;
        private readonly string _librariesPath;
        private readonly string _vcRedistPath;

        // Windows API imports
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        // Critical Visual C++ Runtime libraries
        private static readonly Dictionary<string, VCRedistLibrary> RequiredLibraries = new()
        {
            // Visual C++ 2013 Redistributable (x86)
            ["msvcr120.dll"] = new VCRedistLibrary
            {
                Name = "msvcr120.dll",
                Description = "Microsoft Visual C++ 2013 Runtime (x86)",
                Version = "12.0",
                Architecture = "x86",
                IsRequired = true
            },
            ["msvcp120.dll"] = new VCRedistLibrary
            {
                Name = "msvcp120.dll",
                Description = "Microsoft Visual C++ 2013 C++ Runtime (x86)",
                Version = "12.0",
                Architecture = "x86",
                IsRequired = true
            },

            // Visual C++ 2015-2022 Redistributable (x86)
            ["msvcr140.dll"] = new VCRedistLibrary
            {
                Name = "msvcr140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x86)",
                Version = "14.0",
                Architecture = "x86",
                IsRequired = true
            },
            ["msvcp140.dll"] = new VCRedistLibrary
            {
                Name = "msvcp140.dll",
                Description = "Microsoft Visual C++ 2015-2022 C++ Runtime (x86)",
                Version = "14.0",
                Architecture = "x86",
                IsRequired = true
            },
            ["vcruntime140.dll"] = new VCRedistLibrary
            {
                Name = "vcruntime140.dll",
                Description = "Microsoft Visual C++ 2015-2022 Runtime (x86)",
                Version = "14.0",
                Architecture = "x86",
                IsRequired = true
            },

            // Universal CRT libraries
            ["api-ms-win-crt-runtime-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-runtime-l1-1-0.dll",
                Description = "Universal CRT Runtime",
                Version = "10.0",
                Architecture = "x86",
                IsRequired = true
            },
            ["api-ms-win-crt-heap-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-heap-l1-1-0.dll",
                Description = "Universal CRT Heap",
                Version = "10.0",
                Architecture = "x86",
                IsRequired = true
            },
            ["api-ms-win-crt-string-l1-1-0.dll"] = new VCRedistLibrary
            {
                Name = "api-ms-win-crt-string-l1-1-0.dll",
                Description = "Universal CRT String",
                Version = "10.0",
                Architecture = "x86",
                IsRequired = true
            }
        };

        public VCRedistBundler(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _applicationPath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            _librariesPath = Path.Combine(_applicationPath, "Libraries");
            _vcRedistPath = Path.Combine(_librariesPath, "VCRedist");
        }

        /// <summary>
        /// Bundles all required Visual C++ Redistributable libraries
        /// </summary>
        public async Task<bool> BundleVCRedistLibrariesAsync()
        {
            try
            {
                // Check if VC++ download is disabled via environment variable
                string skipDownload = Environment.GetEnvironmentVariable("SKIP_VCREDIST_DOWNLOAD");
                if (!string.IsNullOrEmpty(skipDownload) && skipDownload.ToLower() == "true")
                {
                    _logger.LogInformation("Skipping VC++ Redistributable bundling due to SKIP_VCREDIST_DOWNLOAD environment variable", "VCRedistBundler");
                    return true; // Return true to indicate success (skip operation)
                }

                _logger.LogInformation("Starting Visual C++ Redistributable bundling", "VCRedistBundler");

                // Create VCRedist directory
                if (!Directory.Exists(_vcRedistPath))
                {
                    Directory.CreateDirectory(_vcRedistPath);
                    _logger.LogInformation($"Created VCRedist directory: {_vcRedistPath}", "VCRedistBundler");
                }

                // Copy system libraries
                await CopySystemVCRedistLibrariesAsync();

                // Extract embedded libraries
                await ExtractEmbeddedVCRedistLibrariesAsync();

                // Verify bundling
                bool success = await VerifyVCRedistBundlingAsync();

                _logger.LogInformation($"Visual C++ Redistributable bundling completed. Success: {success}", "VCRedistBundler");
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error bundling Visual C++ Redistributables: {ex.Message}", "VCRedistBundler", ex);
                return false;
            }
        }

        private async Task CopySystemVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Copying system Visual C++ Redistributable libraries", "VCRedistBundler");

            var systemPaths = new[]
            {
                Environment.GetFolderPath(Environment.SpecialFolder.System),
                Environment.GetFolderPath(Environment.SpecialFolder.SystemX86),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "SysWOW64"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Microsoft Visual Studio", "2022", "Redistributable", "MSVC"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Microsoft Visual Studio", "2019", "Redistributable", "MSVC"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Microsoft Visual Studio", "2017", "Redistributable", "MSVC")
            };

            foreach (var library in RequiredLibraries.Values)
            {
                string targetPath = Path.Combine(_vcRedistPath, library.Name);

                if (!File.Exists(targetPath))
                {
                    bool found = false;
                    foreach (string systemPath in systemPaths)
                    {
                        if (!Directory.Exists(systemPath))
                            continue;

                        // Check direct path
                        string sourcePath = Path.Combine(systemPath, library.Name);
                        if (File.Exists(sourcePath))
                        {
                            try
                            {
                                File.Copy(sourcePath, targetPath, true);
                                _logger.LogInformation($"Copied {library.Name} from {sourcePath}", "VCRedistBundler");
                                found = true;
                                break;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning($"Failed to copy {library.Name} from {sourcePath}: {ex.Message}", "VCRedistBundler");
                            }
                        }

                        // Check subdirectories for MSVC redistributables
                        try
                        {
                            var subdirs = Directory.GetDirectories(systemPath, "*", SearchOption.AllDirectories);
                            foreach (string subdir in subdirs)
                            {
                                string subSourcePath = Path.Combine(subdir, library.Name);
                                if (File.Exists(subSourcePath))
                                {
                                    try
                                    {
                                        File.Copy(subSourcePath, targetPath, true);
                                        _logger.LogInformation($"Copied {library.Name} from {subSourcePath}", "VCRedistBundler");
                                        found = true;
                                        break;
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogDebug($"Failed to copy {library.Name} from {subSourcePath}: {ex.Message}", "VCRedistBundler");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug($"Error searching subdirectories in {systemPath}: {ex.Message}", "VCRedistBundler");
                        }

                        if (found) break;
                    }

                    if (!found && library.IsRequired)
                    {
                        _logger.LogWarning($"Required Visual C++ library not found in system: {library.Name}", "VCRedistBundler");
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task ExtractEmbeddedVCRedistLibrariesAsync()
        {
            _logger.LogInformation("Extracting embedded Visual C++ Redistributable libraries", "VCRedistBundler");

            var assembly = Assembly.GetExecutingAssembly();
            var resourceNames = assembly.GetManifestResourceNames();

            foreach (string resourceName in resourceNames)
            {
                if (resourceName.Contains("vcredist") || resourceName.Contains("msvc"))
                {
                    try
                    {
                        string fileName = Path.GetFileName(resourceName);
                        if (RequiredLibraries.ContainsKey(fileName))
                        {
                            string targetPath = Path.Combine(_vcRedistPath, fileName);

                            if (!File.Exists(targetPath))
                            {
                                using var stream = assembly.GetManifestResourceStream(resourceName);
                                if (stream != null)
                                {
                                    using var fileStream = File.Create(targetPath);
                                    await stream.CopyToAsync(fileStream);
                                    _logger.LogInformation($"Extracted embedded VC++ library: {fileName}", "VCRedistBundler");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to extract embedded VC++ resource {resourceName}: {ex.Message}", "VCRedistBundler");
                    }
                }
            }
        }

        private async Task<bool> VerifyVCRedistBundlingAsync()
        {
            _logger.LogInformation("Verifying Visual C++ Redistributable bundling", "VCRedistBundler");

            int foundCount = 0;
            int requiredCount = 0;

            foreach (var library in RequiredLibraries.Values)
            {
                if (library.IsRequired)
                {
                    requiredCount++;
                    string targetPath = Path.Combine(_vcRedistPath, library.Name);

                    if (File.Exists(targetPath))
                    {
                        foundCount++;
                        _logger.LogInformation($"✓ Verified VC++ library: {library.Name}", "VCRedistBundler");

                        // Try to load the library to verify it's valid
                        try
                        {
                            IntPtr handle = LoadLibrary(targetPath);
                            if (handle != IntPtr.Zero)
                            {
                                FreeLibrary(handle);
                                _logger.LogDebug($"Successfully loaded and verified: {library.Name}", "VCRedistBundler");
                            }
                            else
                            {
                                _logger.LogWarning($"Library exists but failed to load: {library.Name}", "VCRedistBundler");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning($"Error verifying library {library.Name}: {ex.Message}", "VCRedistBundler");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"✗ Missing VC++ library: {library.Name}", "VCRedistBundler");
                    }
                }
            }

            double successRate = requiredCount > 0 ? (double)foundCount / requiredCount * 100 : 100;
            _logger.LogInformation($"VC++ Redistributable verification: {foundCount}/{requiredCount} ({successRate:F1}%) required libraries found", "VCRedistBundler");

            await Task.CompletedTask;
            return successRate >= 70; // Consider successful if 70% or more libraries are found
        }

        /// <summary>
        /// Gets the bundling status
        /// </summary>
        public async Task<VCRedistStatus> GetStatusAsync()
        {
            var status = new VCRedistStatus
            {
                VCRedistPath = _vcRedistPath,
                AvailableLibraries = new List<string>(),
                MissingLibraries = new List<string>()
            };

            foreach (var library in RequiredLibraries.Values)
            {
                string targetPath = Path.Combine(_vcRedistPath, library.Name);
                if (File.Exists(targetPath))
                {
                    var fileInfo = new FileInfo(targetPath);
                    status.AvailableLibraries.Add($"{library.Name} ({library.Description}, {fileInfo.Length} bytes)");
                }
                else
                {
                    status.MissingLibraries.Add($"{library.Name} ({library.Description})");
                }
            }

            await Task.CompletedTask;
            return status;
        }
    }

    /// <summary>
    /// Information about a Visual C++ Redistributable library
    /// </summary>
    public class VCRedistLibrary
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Architecture { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
    }

    /// <summary>
    /// Status of Visual C++ Redistributable bundling
    /// </summary>
    public class VCRedistStatus
    {
        public string VCRedistPath { get; set; } = string.Empty;
        public List<string> AvailableLibraries { get; set; } = new();
        public List<string> MissingLibraries { get; set; } = new();
    }
}
