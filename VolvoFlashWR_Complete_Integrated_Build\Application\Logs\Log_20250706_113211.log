Log started at 7/6/2025 11:32:11 AM
2025-07-06 11:32:11.154 [Information] LoggingService: Logging service initialized
2025-07-06 11:32:11.168 [Information] App: Starting integrated application initialization
2025-07-06 11:32:11.169 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-06 11:32:11.172 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-06 11:32:11.174 [Information] IntegratedStartupService: Setting up application environment
2025-07-06 11:32:11.174 [Information] IntegratedStartupService: Application environment setup completed
2025-07-06 11:32:11.176 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-06 11:32:11.178 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-06 11:32:11.182 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-06 11:32:11.194 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-06 11:32:11.201 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:32:11.206 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:32:11.215 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:32:11.217 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-06 11:32:11.220 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-06 11:32:11.220 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-06 11:32:11.222 [Warning] VCRedistBundler: Library exists but failed to load: msvcr120.dll
2025-07-06 11:32:11.222 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-06 11:32:11.223 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-06 11:32:11.223 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-06 11:32:11.223 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-06 11:32:11.224 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-06 11:32:11.224 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-06 11:32:11.225 [Warning] VCRedistBundler: Library exists but failed to load: vcruntime140.dll
2025-07-06 11:32:11.225 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-06 11:32:11.226 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-06 11:32:11.226 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-06 11:32:11.234 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-06 11:32:11.235 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-06 11:32:11.235 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-06 11:32:11.242 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-06 11:32:11.242 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-06 11:32:11.244 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-06 11:32:11.246 [Information] LibraryExtractor: Starting library extraction process
2025-07-06 11:32:11.250 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-06 11:32:11.252 [Information] LibraryExtractor: Copying system libraries
2025-07-06 11:32:11.258 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-06 11:32:11.268 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-06 11:32:34.633 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-06 11:33:18.400 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-06 11:33:59.331 [Information] LibraryExtractor: Downloading missing library: vcruntime140.dll
2025-07-06 11:34:40.548 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll
